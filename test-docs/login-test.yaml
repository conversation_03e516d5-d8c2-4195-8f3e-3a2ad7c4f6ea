name: "login-test"
description: "验证登录功能的各种场景"
target: "https://example.com/login"
steps:
  - goal: "访问登录页面"
    operation: "导航"
    content: "https://example.com/login"

  - goal: "等待登录表单加载"
    operation: "等待"
    content: "login-form"

  - goal: "填写有效用户名和密码"
    operation: "填写"
    content: { username: "valid_user", password: "valid_password" }

  - goal: "点击登录按钮进行有效登录"
    operation: "点击"
    content: "login-submit-button"

  - goal: "验证有效登录是否成功"
    operation: "检查"
    content: "用户仪表盘可见"

  - goal: "返回登录页面进行无效用户名测试"
    operation: "导航"
    content: "https://example.com/login"

  - goal: "等待登录表单加载"
    operation: "等待"
    content: "login-form"

  - goal: "填写无效用户名和有效密码"
    operation: "填写"
    content: { username: "invalid_user", password: "valid_password" }

  - goal: "点击登录按钮进行无效用户名登录"
    operation: "点击"
    content: "login-submit-button"

  - goal: "验证无效用户名的错误信息"
    operation: "检查"
    content: "用户名不存在的错误提示显示"

  - goal: "返回登录页面进行无效密码测试"
    operation: "导航"
    content: "https://example.com/login"

  - goal: "等待登录表单加载"
    operation: "等待"
    content: "login-form"

  - goal: "填写有效用户名和无效密码"
    operation: "填写"
    content: { username: "valid_user", password: "invalid_password" }

  - goal: "点击登录按钮进行无效密码登录"
    operation: "点击"
    content: "login-submit-button"

  - goal: "验证无效密码的错误信息"
    operation: "检查"
    content: "密码错误的提示信息显示"

  - goal: "返回登录页面进行空字段测试"
    operation: "导航"
    content: "https://example.com/login"

  - goal: "等待登录表单加载"
    operation: "等待"
    content: "login-form"

  - goal: "点击登录按钮而不填写任何字段"
    operation: "点击"
    content: "login-submit-button"

  - goal: "验证空字段的错误提示"
    operation: "检查"
    content: "用户名和密码不能为空的提示信息显示"