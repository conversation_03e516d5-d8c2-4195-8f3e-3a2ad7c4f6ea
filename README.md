# AI自动化测试 VS Code 插件

通过AI驱动Playwright的MCP进行自然语义自动化测试的VS Code插件。

## 功能特性

- **文档管理**: 自动扫描和组织测试文档(.md)、步骤文件(.yaml)和结果文件(.result.md)
- **AI集成**: 使用OpenAI兼容的API从自然语言测试描述生成结构化测试步骤
- **MCP集成**: 通过Playwright MCP协议执行浏览器自动化测试
- **实时反馈**: 在测试执行期间提供实时进度更新和状态信息
- **并行执行**: 支持通过队列管理并发运行多个测试

## 安装

1. 从VS Code扩展市场安装插件
2. 或者从VSIX文件手动安装

## 使用方法

1. 打开命令面板 (`Ctrl+Shift+P` 或 `Cmd+Shift+P`)
2. 运行 "打开AI测试面板" 命令
3. 配置AI服务和测试目录
4. 扫描测试文档并执行测试

## 配置

在插件设置中配置以下选项：

- **测试目录**: 测试文档的目录路径
- **AI服务API地址**: OpenAI兼容的API端点
- **API密钥**: 用于AI服务的API密钥
- **模型**: 要使用的AI模型名称

## 开发

```bash
# 安装依赖
pnpm install

# 编译
pnpm run compile

# 打包
pnpm run package

# 监视模式
pnpm run watch
```

## 许可证

MIT License - 查看 [LICENSE](LICENSE) 文件了解详情。