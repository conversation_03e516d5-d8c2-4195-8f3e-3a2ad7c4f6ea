import * as vscode from 'vscode';

export interface AIConfig {
    apiUrl: string;
    apiKey: string;
    model: string;
}

export interface ValidationResult {
    isValid: boolean;
    errors: string[];
}

export class ConfigurationManager {
    private static instance: ConfigurationManager;
    
    public static getInstance(): ConfigurationManager {
        if (!ConfigurationManager.instance) {
            ConfigurationManager.instance = new ConfigurationManager();
        }
        return ConfigurationManager.instance;
    }

    public getAIConfig(): AIConfig {
        const config = vscode.workspace.getConfiguration('ai-test');
        return {
            apiUrl: config.get('aiService.apiUrl', ''),
            apiKey: config.get('aiService.apiKey', ''),
            model: config.get('aiService.model', 'gpt-3.5-turbo')
        };
    }

    public async setAIConfig(aiConfig: AIConfig): Promise<void> {
        const config = vscode.workspace.getConfiguration('ai-test');
        await config.update('aiService.apiUrl', aiConfig.apiUrl, vscode.ConfigurationTarget.Workspace);
        await config.update('aiService.apiKey', aiConfig.apiKey, vscode.ConfigurationTarget.Workspace);
        await config.update('aiService.model', aiConfig.model, vscode.ConfigurationTarget.Workspace);
    }

    public getTestDirectory(): string {
        const config = vscode.workspace.getConfiguration('ai-test');
        const configuredPath = config.get('testDirectory', '');
        
        if (!configuredPath) {
            return '';
        }
        
        // 如果是相对路径，转换为绝对路径
        if (!require('path').isAbsolute(configuredPath)) {
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (workspaceFolder) {
                return require('path').join(workspaceFolder.uri.fsPath, configuredPath);
            }
        }
        
        return configuredPath;
    }

    public async setTestDirectory(path: string): Promise<void> {
        const config = vscode.workspace.getConfiguration('ai-test');
        await config.update('testDirectory', path, vscode.ConfigurationTarget.Workspace);
    }

    public async validateConfig(): Promise<ValidationResult> {
        const errors: string[] = [];
        const aiConfig = this.getAIConfig();
        const testDirectory = this.getTestDirectory();

        if (!aiConfig.apiUrl) {
            errors.push('AI服务API地址未配置');
        }

        if (!aiConfig.apiKey) {
            errors.push('AI服务API密钥未配置');
        }

        if (!testDirectory) {
            errors.push('测试目录未配置');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }
}