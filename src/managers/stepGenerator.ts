import * as fs from 'fs';
import * as path from 'path';
import * as yaml from 'js-yaml';
import { AIServiceClient, AIServiceError } from './aiService';
import { ConfigurationManager } from './config';

/**
 * 测试步骤接口
 */
export interface TestStep {
    goal: string;
    operation: string;
    content: string;
}

/**
 * 测试步骤文档接口
 */
export interface TestStepDocument {
    name: string;
    description: string;
    target: string;
    steps: TestStep[];
}

/**
 * 步骤生成结果接口
 */
export interface StepGenerationResult {
    success: boolean;
    filePath?: string;
    content?: string;
    error?: string;
    errorType?: 'validation' | 'ai_service' | 'file_system' | 'parsing';
}

/**
 * 步骤生成器类
 * 负责解析测试文档内容，调用AI服务生成步骤，验证和保存YAML文档
 */
export class StepGenerator {
    private static instance: StepGenerator;
    private aiClient: AIServiceClient;
    private configManager: ConfigurationManager;

    private constructor() {
        this.aiClient = AIServiceClient.getInstance();
        this.configManager = ConfigurationManager.getInstance();
    }

    /**
     * 获取单例实例
     */
    public static getInstance(): StepGenerator {
        if (!StepGenerator.instance) {
            StepGenerator.instance = new StepGenerator();
        }
        return StepGenerator.instance;
    }

    /**
     * 为测试文档生成步骤文件
     * @param testFilePath 测试文档文件路径
     * @returns 生成结果
     */
    public async generateStepsForTest(testFilePath: string): Promise<StepGenerationResult> {
        try {
            console.log(`开始为测试文档生成步骤: ${testFilePath}`);

            // 1. 验证输入参数
            const validationResult = this.validateInput(testFilePath);
            if (!validationResult.success) {
                return validationResult;
            }

            // 2. 读取测试文档内容
            const testContent = await this.readTestDocument(testFilePath);
            if (!testContent) {
                return {
                    success: false,
                    error: '无法读取测试文档内容',
                    errorType: 'file_system'
                };
            }

            // 3. 解析测试文档，提取关键信息
            const parsedContent = this.parseTestDocument(testContent, testFilePath);

            // 4. 配置AI服务
            const configResult = this.configureAIService();
            if (!configResult.success) {
                return configResult;
            }

            // 5. 调用AI服务生成步骤
            const aiResponse = await this.callAIService(parsedContent);

            // 6. 验证AI生成的YAML内容
            const validatedSteps = this.validateGeneratedSteps(aiResponse);
            if (!validatedSteps.success) {
                return validatedSteps;
            }

            // 7. 保存步骤文档
            const stepFilePath = this.getStepFilePath(testFilePath);
            const saveResult = await this.saveStepDocument(stepFilePath, aiResponse);
            
            if (saveResult.success) {
                console.log(`步骤文档生成成功: ${stepFilePath}`);
                return {
                    success: true,
                    filePath: stepFilePath,
                    content: aiResponse
                };
            } else {
                return saveResult;
            }

        } catch (error) {
            console.error('生成步骤时发生错误:', error);

            // 提供更详细的错误信息
            let errorMessage = '生成步骤失败';
            let errorType: 'validation' | 'ai_service' | 'file_system' | 'parsing' = 'ai_service';

            if (error instanceof Error) {
                errorMessage = `生成步骤失败: ${error.message}`;

                // 根据错误消息判断错误类型
                if (error.message.includes('配置') || error.message.includes('API')) {
                    errorType = 'validation';
                } else if (error.message.includes('文件') || error.message.includes('目录')) {
                    errorType = 'file_system';
                } else if (error.message.includes('解析') || error.message.includes('YAML')) {
                    errorType = 'parsing';
                }
            }

            return {
                success: false,
                error: errorMessage,
                errorType
            };
        }
    }

    /**
     * 批量生成步骤文档
     * @param testFilePaths 测试文档文件路径数组
     * @returns 生成结果数组
     */
    public async generateStepsForMultipleTests(testFilePaths: string[]): Promise<StepGenerationResult[]> {
        const results: StepGenerationResult[] = [];
        
        for (const testFilePath of testFilePaths) {
            try {
                const result = await this.generateStepsForTest(testFilePath);
                results.push(result);
                
                // 添加延迟，避免API调用过于频繁
                if (testFilePaths.length > 1) {
                    await this.delay(1000); // 1秒延迟
                }
            } catch (error) {
                results.push({
                    success: false,
                    error: `处理文件 ${testFilePath} 时出错: ${error instanceof Error ? error.message : '未知错误'}`,
                    errorType: 'ai_service'
                });
            }
        }
        
        return results;
    }

    /**
     * 验证输入参数
     * @param testFilePath 测试文档路径
     * @returns 验证结果
     */
    private validateInput(testFilePath: string): StepGenerationResult {
        if (!testFilePath) {
            return {
                success: false,
                error: '测试文档路径不能为空',
                errorType: 'validation'
            };
        }

        if (!fs.existsSync(testFilePath)) {
            return {
                success: false,
                error: `测试文档不存在: ${testFilePath}`,
                errorType: 'file_system'
            };
        }

        if (!testFilePath.endsWith('.md')) {
            return {
                success: false,
                error: '只支持Markdown格式的测试文档',
                errorType: 'validation'
            };
        }

        return { success: true };
    }

    /**
     * 读取测试文档内容
     * @param testFilePath 测试文档路径
     * @returns 文档内容
     */
    private async readTestDocument(testFilePath: string): Promise<string | null> {
        try {
            const content = fs.readFileSync(testFilePath, 'utf-8');
            return content.trim();
        } catch (error) {
            console.error('读取测试文档失败:', error);
            return null;
        }
    }

    /**
     * 解析测试文档内容，提取关键信息
     * @param content 文档内容
     * @param filePath 文件路径
     * @returns 解析后的内容
     */
    private parseTestDocument(content: string, filePath: string): string {
        // 提取文件名作为测试名称
        const fileName = path.basename(filePath, '.md');
        
        // 构建增强的测试内容，包含文件名信息
        const enhancedContent = `# 测试文档: ${fileName}

${content}

---
注意：请根据以上测试文档内容生成详细的测试步骤。`;

        return enhancedContent;
    }

    /**
     * 配置AI服务
     * @returns 配置结果
     */
    private configureAIService(): StepGenerationResult {
        try {
            const aiConfig = this.configManager.getAIConfig();
            
            if (!aiConfig.apiUrl || !aiConfig.apiKey) {
                return {
                    success: false,
                    error: 'AI服务配置不完整，请检查API地址和密钥设置',
                    errorType: 'validation'
                };
            }

            this.aiClient.configure(aiConfig);
            return { success: true };
        } catch (error) {
            return {
                success: false,
                error: `配置AI服务失败: ${error instanceof Error ? error.message : '未知错误'}`,
                errorType: 'ai_service'
            };
        }
    }

    /**
     * 调用AI服务生成步骤
     * @param testContent 测试内容
     * @returns AI生成的内容
     */
    private async callAIService(testContent: string): Promise<string> {
        try {
            const response = await this.aiClient.generateSteps(testContent);
            return response;
        } catch (error) {
            if (error instanceof AIServiceError) {
                throw new Error(`AI服务错误 (${error.type}): ${error.message}`);
            }
            throw error;
        }
    }

    /**
     * 验证AI生成的步骤内容
     * @param content AI生成的内容
     * @returns 验证结果
     */
    private validateGeneratedSteps(content: string): StepGenerationResult {
        try {
            // 提取YAML内容（去除可能的markdown代码块标记）
            const yamlContent = this.extractYamlContent(content);
            
            if (!yamlContent) {
                return {
                    success: false,
                    error: '生成的内容中未找到有效的YAML格式',
                    errorType: 'parsing'
                };
            }

            // 解析YAML内容
            const parsed = yaml.load(yamlContent) as TestStepDocument;
            
            // 验证必需字段
            if (!parsed.name || !parsed.description || !parsed.steps) {
                return {
                    success: false,
                    error: 'YAML文档缺少必需字段 (name, description, steps)',
                    errorType: 'validation'
                };
            }

            if (!Array.isArray(parsed.steps) || parsed.steps.length === 0) {
                return {
                    success: false,
                    error: '步骤列表为空或格式不正确',
                    errorType: 'validation'
                };
            }

            // 验证每个步骤的格式
            for (let i = 0; i < parsed.steps.length; i++) {
                const step = parsed.steps[i];
                if (!step.goal || !step.operation || !step.content) {
                    return {
                        success: false,
                        error: `步骤 ${i + 1} 缺少必需字段 (goal, operation, content)`,
                        errorType: 'validation'
                    };
                }
            }

            return { success: true };
        } catch (error) {
            return {
                success: false,
                error: `YAML解析失败: ${error instanceof Error ? error.message : '未知错误'}`,
                errorType: 'parsing'
            };
        }
    }

    /**
     * 从AI响应中提取YAML内容
     * @param content AI响应内容
     * @returns 提取的YAML内容
     */
    private extractYamlContent(content: string): string | null {
        // 移除可能的markdown代码块标记
        const yamlBlockRegex = /```(?:yaml|yml)?\s*([\s\S]*?)\s*```/i;
        const match = content.match(yamlBlockRegex);
        
        if (match) {
            return match[1].trim();
        }

        // 如果没有代码块标记，尝试直接解析整个内容
        const trimmedContent = content.trim();
        if (trimmedContent.startsWith('name:') || trimmedContent.includes('steps:')) {
            return trimmedContent;
        }

        return null;
    }

    /**
     * 获取步骤文件路径
     * @param testFilePath 测试文档路径
     * @returns 步骤文件路径
     */
    private getStepFilePath(testFilePath: string): string {
        const dir = path.dirname(testFilePath);
        const baseName = path.basename(testFilePath, '.md');
        return path.join(dir, `${baseName}.yaml`);
    }

    /**
     * 保存步骤文档
     * @param filePath 保存路径
     * @param content YAML内容
     * @returns 保存结果
     */
    private async saveStepDocument(filePath: string, content: string): Promise<StepGenerationResult> {
        try {
            // 提取并格式化YAML内容
            const yamlContent = this.extractYamlContent(content);
            if (!yamlContent) {
                return {
                    success: false,
                    error: '无法提取有效的YAML内容',
                    errorType: 'parsing'
                };
            }

            // 确保目录存在
            const dir = path.dirname(filePath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }

            // 保存文件
            fs.writeFileSync(filePath, yamlContent, 'utf-8');
            
            return { success: true };
        } catch (error) {
            return {
                success: false,
                error: `保存步骤文档失败: ${error instanceof Error ? error.message : '未知错误'}`,
                errorType: 'file_system'
            };
        }
    }

    /**
     * 延迟函数
     * @param ms 延迟毫秒数
     */
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
