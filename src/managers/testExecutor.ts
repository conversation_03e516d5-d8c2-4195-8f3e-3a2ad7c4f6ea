import * as vscode from 'vscode';
import { EventEmitter } from 'events';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import * as yaml from 'js-yaml';
import * as fs from 'fs';
import * as path from 'path';
import { AIServiceClient } from './aiService';

/**
 * 测试步骤数据结构
 */
export interface TestStep {
    goal: string;           // 步骤目标描述
    operation: string;      // 操作类型：导航、点击、填写、检查等
    content: string;        // 操作内容或参数
    timeout?: number;       // 超时时间（秒）
    screenshot?: boolean;   // 是否截图
}

/**
 * 测试文档结构
 */
export interface TestDocument {
    name: string;           // 测试名称
    description: string;    // 测试描述
    target: string;         // 目标URL
    steps: TestStep[];      // 测试步骤列表
}

/**
 * 步骤执行结果
 */
export interface StepResult {
    stepIndex: number;      // 步骤索引
    goal: string;           // 步骤目标
    status: 'success' | 'failed' | 'skipped';  // 执行状态
    duration: number;       // 执行时长（毫秒）
    error?: string;         // 错误信息
    screenshot?: string;    // 截图文件路径
    details?: string;       // 详细信息
}

/**
 * 测试执行结果
 */
export interface TestResult {
    testName: string;       // 测试名称
    status: 'success' | 'failed' | 'cancelled';  // 测试状态
    startTime: Date;        // 开始时间
    endTime: Date;          // 结束时间
    duration: number;       // 总时长（毫秒）
    steps: StepResult[];    // 步骤结果列表
    error?: string;         // 整体错误信息
    screenshots: string[];  // 截图文件列表
}

/**
 * 测试执行进度
 */
export interface TestProgress {
    testName: string;       // 测试名称
    currentStep: number;    // 当前步骤索引
    totalSteps: number;     // 总步骤数
    stepDescription: string; // 当前步骤描述
    status: 'running' | 'completed' | 'failed' | 'cancelled';  // 执行状态
}

/**
 * MCP客户端配置
 */
export interface MCPClientConfig {
    command: string;        // MCP服务命令
    args: string[];         // 命令参数
    timeout: number;        // 连接超时时间
}

/**
 * 测试执行器类
 * 负责与Playwright MCP服务通信，执行测试步骤，生成测试结果
 */
export class TestExecutor extends EventEmitter {
    private static instance: TestExecutor;
    private mcpClient: Client | null = null;
    private mcpTransport: StdioClientTransport | null = null;
    private isConnected: boolean = false;
    private currentExecution: {
        testName: string;
        cancelled: boolean;
        startTime: Date;
    } | null = null;

    private constructor() {
        super();
    }

    /**
     * 获取单例实例
     */
    public static getInstance(): TestExecutor {
        if (!TestExecutor.instance) {
            TestExecutor.instance = new TestExecutor();
        }
        return TestExecutor.instance;
    }

    /**
     * 连接到Playwright MCP服务
     */
    public async connectToMCP(config?: MCPClientConfig): Promise<boolean> {
        try {
            console.log('正在连接到Playwright MCP服务...');

            // 默认配置
            const mcpConfig = config || {
                command: 'npx',
                args: ['@playwright/mcp@latest'],
                timeout: 30000
            };

            // 创建MCP客户端传输
            this.mcpTransport = new StdioClientTransport({
                command: mcpConfig.command,
                args: mcpConfig.args
            });

            // 创建MCP客户端
            this.mcpClient = new Client({
                name: 'ai-test-executor',
                version: '1.0.0'
            });

            // 连接到MCP服务
            await this.mcpClient.connect(this.mcpTransport);
            this.isConnected = true;

            console.log('成功连接到Playwright MCP服务');
            this.emit('connected');
            return true;

        } catch (error) {
            console.error('连接MCP服务失败:', error);
            this.isConnected = false;
            this.emit('connectionError', error);
            return false;
        }
    }

    /**
     * 断开MCP连接
     */
    public async disconnectFromMCP(): Promise<void> {
        try {
            if (this.mcpClient && this.isConnected) {
                await this.mcpClient.close();
                this.mcpClient = null;
                this.mcpTransport = null;
                this.isConnected = false;
                console.log('已断开MCP连接');
                this.emit('disconnected');
            }
        } catch (error) {
            console.error('断开MCP连接时出错:', error);
        }
    }

    /**
     * 检查MCP连接状态
     */
    public isConnectedToMCP(): boolean {
        return this.isConnected && this.mcpClient !== null;
    }

    /**
     * 解析YAML步骤文档
     */
    public parseStepDocument(filePath: string): TestDocument | null {
        try {
            console.log(`解析步骤文档: ${filePath}`);

            if (!fs.existsSync(filePath)) {
                throw new Error(`步骤文档不存在: ${filePath}`);
            }

            const content = fs.readFileSync(filePath, 'utf-8');
            const document = yaml.load(content) as TestDocument;

            // 验证文档结构
            if (!document.name || !document.steps || !Array.isArray(document.steps)) {
                throw new Error('无效的步骤文档格式');
            }

            console.log(`成功解析步骤文档，包含 ${document.steps.length} 个步骤`);
            return document;

        } catch (error) {
            console.error('解析步骤文档失败:', error);
            return null;
        }
    }

    /**
     * 执行单个测试
     */
    public async executeTest(stepFilePath: string): Promise<TestResult> {
        const testName = path.basename(stepFilePath, '.yaml');
        
        try {
            console.log(`开始执行测试: ${testName}`);

            // 检查MCP连接
            if (!this.isConnectedToMCP()) {
                const connected = await this.connectToMCP();
                if (!connected) {
                    throw new Error('无法连接到Playwright MCP服务');
                }
            }

            // 解析步骤文档
            const testDocument = this.parseStepDocument(stepFilePath);
            if (!testDocument) {
                throw new Error('无法解析步骤文档');
            }

            // 初始化执行状态
            this.currentExecution = {
                testName,
                cancelled: false,
                startTime: new Date()
            };

            const startTime = new Date();
            const stepResults: StepResult[] = [];
            const screenshots: string[] = [];

            // 发送开始事件
            this.emit('testStarted', {
                testName,
                currentStep: 0,
                totalSteps: testDocument.steps.length,
                stepDescription: '准备执行测试',
                status: 'running'
            } as TestProgress);

            // 执行测试步骤
            for (let i = 0; i < testDocument.steps.length; i++) {
                // 检查是否被取消
                if (this.currentExecution?.cancelled) {
                    throw new Error('测试执行被取消');
                }

                const step = testDocument.steps[i];
                
                // 发送进度更新
                this.emit('stepStarted', {
                    testName,
                    currentStep: i + 1,
                    totalSteps: testDocument.steps.length,
                    stepDescription: step.goal,
                    status: 'running'
                } as TestProgress);

                // 执行步骤
                const stepResult = await this.executeStep(step, i, testDocument);
                stepResults.push(stepResult);

                if (stepResult.screenshot) {
                    screenshots.push(stepResult.screenshot);
                }

                // 发送步骤完成事件
                this.emit('stepCompleted', stepResult);

                // 如果步骤失败，停止执行
                if (stepResult.status === 'failed') {
                    break;
                }
            }

            const endTime = new Date();
            const duration = endTime.getTime() - startTime.getTime();

            // 确定整体测试状态
            const hasFailedSteps = stepResults.some(r => r.status === 'failed');
            const status = this.currentExecution?.cancelled ? 'cancelled' : 
                          hasFailedSteps ? 'failed' : 'success';

            const result: TestResult = {
                testName,
                status,
                startTime,
                endTime,
                duration,
                steps: stepResults,
                screenshots
            };

            // 发送测试完成事件
            this.emit('testCompleted', result);

            console.log(`测试执行完成: ${testName}, 状态: ${status}, 耗时: ${duration}ms`);
            return result;

        } catch (error) {
            const endTime = new Date();
            const duration = this.currentExecution ? 
                endTime.getTime() - this.currentExecution.startTime.getTime() : 0;

            const errorMessage = error instanceof Error ? error.message : '未知错误';
            
            const result: TestResult = {
                testName,
                status: 'failed',
                startTime: this.currentExecution?.startTime || new Date(),
                endTime,
                duration,
                steps: [],
                screenshots: [],
                error: errorMessage
            };

            console.error(`测试执行失败: ${testName}`, error);
            this.emit('testFailed', result);
            return result;

        } finally {
            this.currentExecution = null;
        }
    }

    /**
     * 执行单个测试步骤
     */
    private async executeStep(step: TestStep, stepIndex: number, testDocument: TestDocument): Promise<StepResult> {
        const stepStartTime = Date.now();

        try {
            console.log(`执行步骤 ${stepIndex + 1}: ${step.goal}`);

            // 检查MCP连接
            if (!this.isConnectedToMCP()) {
                throw new Error('MCP客户端未连接');
            }

            // 根据操作类型执行相应的MCP工具调用
            const result = await this.executeMCPOperation(step, stepIndex, testDocument);

            const duration = Date.now() - stepStartTime;

            return {
                stepIndex,
                goal: step.goal,
                status: result.success ? 'success' : 'failed',
                duration,
                details: result.details,
                error: result.error,
                screenshot: result.screenshot
            };

        } catch (error) {
            const duration = Date.now() - stepStartTime;
            const errorMessage = error instanceof Error ? error.message : '未知错误';

            return {
                stepIndex,
                goal: step.goal,
                status: 'failed',
                duration,
                error: errorMessage
            };
        }
    }

    /**
     * 执行MCP操作
     */
    private async executeMCPOperation(step: TestStep, stepIndex: number, testDocument: TestDocument): Promise<{
        success: boolean;
        details?: string;
        error?: string;
        screenshot?: string;
    }> {
        try {
            if (!this.mcpClient) {
                throw new Error('MCP客户端未初始化');
            }

            let result: any;
            let details = '';

            switch (step.operation.toLowerCase()) {
                case '导航':
                case 'navigate':
                    // 使用browser_navigate工具
                    result = await this.mcpClient.callTool({
                        name: 'browser_navigate',
                        arguments: {
                            url: step.content || testDocument.target
                        }
                    });
                    details = `导航到: ${step.content || testDocument.target}`;
                    break;

                case '点击':
                case 'click':
                    // 首先获取页面快照以找到元素
                    const snapshot = await this.mcpClient.callTool({
                        name: 'browser_snapshot',
                        arguments: {}
                    });

                    // 这里需要解析快照并找到要点击的元素
                    // 暂时使用简化的实现
                    result = await this.mcpClient.callTool({
                        name: 'browser_click',
                        arguments: {
                            element: step.content,
                            ref: step.content // 这里需要从快照中解析实际的元素引用
                        }
                    });
                    details = `点击元素: ${step.content}`;
                    break;

                case '填写':
                case 'type':
                case 'input':
                    // 解析填写内容
                    const typeContent = this.parseTypeContent(step.content);

                    for (const [field, value] of Object.entries(typeContent)) {
                        result = await this.mcpClient.callTool({
                            name: 'browser_type',
                            arguments: {
                                element: field,
                                ref: field, // 这里需要从快照中解析实际的元素引用
                                text: value
                            }
                        });
                    }
                    details = `填写内容: ${step.content}`;
                    break;

                case '检查':
                case 'verify':
                case 'assert':
                    // 获取页面快照进行检查
                    const verifySnapshot = await this.mcpClient.callTool({
                        name: 'browser_snapshot',
                        arguments: {}
                    });

                    // 这里需要实现内容验证逻辑
                    const verificationResult = this.verifyContent(verifySnapshot, step.content);
                    if (!verificationResult.success) {
                        throw new Error(verificationResult.error);
                    }
                    details = `验证成功: ${step.content}`;
                    break;

                case '等待':
                case 'wait':
                    // 解析等待条件
                    const waitCondition = this.parseWaitCondition(step.content);
                    result = await this.mcpClient.callTool({
                        name: 'browser_wait_for',
                        arguments: waitCondition
                    });
                    details = `等待条件: ${step.content}`;
                    break;

                default:
                    throw new Error(`不支持的操作类型: ${step.operation}`);
            }

            // 如果需要截图
            let screenshot: string | undefined;
            if (step.screenshot) {
                const screenshotResult = await this.mcpClient.callTool({
                    name: 'browser_take_screenshot',
                    arguments: {
                        filename: `step-${stepIndex + 1}-${Date.now()}.png`
                    }
                });
                // 从MCP响应中提取截图文件名
                const filename = `step-${stepIndex + 1}-${Date.now()}.png`;
                screenshot = (screenshotResult as any)?.filename || filename;
            }

            return {
                success: true,
                details,
                screenshot
            };

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            console.error(`MCP操作执行失败:`, error);

            return {
                success: false,
                error: errorMessage
            };
        }
    }

    /**
     * 解析填写内容
     */
    private parseTypeContent(content: any): Record<string, string> {
        const result: Record<string, string> = {};

        // 如果content是对象，直接使用
        if (typeof content === 'object' && content !== null) {
            for (const [key, value] of Object.entries(content)) {
                result[key] = String(value);
            }
            return result;
        }

        // 如果是字符串，尝试解析
        const contentStr = String(content);

        // 支持格式: "用户名: testuser, 密码: password123"
        const pairs = contentStr.split(',').map(pair => pair.trim());

        for (const pair of pairs) {
            const [key, value] = pair.split(':').map(s => s.trim());
            if (key && value) {
                result[key] = value;
            }
        }

        // 如果没有找到键值对，将整个内容作为单个值
        if (Object.keys(result).length === 0) {
            result['input'] = contentStr;
        }

        return result;
    }

    /**
     * 解析等待条件
     */
    private parseWaitCondition(content: string): any {
        // 支持格式: "文本: 登录成功" 或 "时间: 3秒"
        if (content.includes('文本:') || content.includes('text:')) {
            const text = content.replace(/^(文本|text):\s*/, '');
            return { text };
        }

        if (content.includes('时间:') || content.includes('time:')) {
            const timeStr = content.replace(/^(时间|time):\s*/, '');
            const time = parseInt(timeStr.replace(/[^\d]/g, ''));
            return { time };
        }

        // 默认等待文本出现
        return { text: content };
    }

    /**
     * 验证内容
     */
    private verifyContent(snapshot: any, expectedContent: string): { success: boolean; error?: string } {
        try {
            // 这里需要实现实际的内容验证逻辑
            // 暂时简化为检查快照中是否包含期望的文本
            const snapshotText = JSON.stringify(snapshot).toLowerCase();
            const expectedText = expectedContent.toLowerCase();

            if (snapshotText.includes(expectedText)) {
                return { success: true };
            } else {
                return {
                    success: false,
                    error: `页面中未找到期望的内容: ${expectedContent}`
                };
            }
        } catch (error) {
            return {
                success: false,
                error: `内容验证失败: ${error instanceof Error ? error.message : '未知错误'}`
            };
        }
    }

    /**
     * 构建步骤执行的AI提示词
     */
    private buildStepExecutionPrompt(step: TestStep, stepIndex: number, testDocument: TestDocument): string {
        return `作为专业的Web自动化测试工程师，请使用Playwright MCP工具执行以下测试步骤：

测试名称: ${testDocument.name}
测试描述: ${testDocument.description}
目标网站: ${testDocument.target}

当前步骤 (${stepIndex + 1}):
- 目标: ${step.goal}
- 操作类型: ${step.operation}
- 操作内容: ${step.content}
${step.timeout ? `- 超时时间: ${step.timeout}秒` : ''}

请根据操作类型选择合适的Playwright MCP工具：
- 导航: 使用 browser_navigate
- 点击: 使用 browser_click
- 填写: 使用 browser_type
- 检查: 使用 browser_snapshot 然后验证内容
- 等待: 使用 browser_wait_for

执行步骤并返回结果。如果需要截图，请使用 browser_take_screenshot。`;
    }

    /**
     * 取消当前测试执行
     */
    public cancelExecution(): void {
        if (this.currentExecution) {
            console.log(`取消测试执行: ${this.currentExecution.testName}`);
            this.currentExecution.cancelled = true;
            this.emit('testCancelled', this.currentExecution.testName);
        }
    }

    /**
     * 执行多个测试
     */
    public async executeMultipleTests(stepFilePaths: string[]): Promise<TestResult[]> {
        const results: TestResult[] = [];
        
        for (const filePath of stepFilePaths) {
            if (this.currentExecution?.cancelled) {
                break;
            }
            
            const result = await this.executeTest(filePath);
            results.push(result);
        }
        
        return results;
    }

    /**
     * 获取可用的MCP工具列表
     */
    public async getAvailableTools(): Promise<any[]> {
        if (!this.isConnectedToMCP() || !this.mcpClient) {
            throw new Error('MCP客户端未连接');
        }

        try {
            const tools = await this.mcpClient.listTools();
            return tools.tools || [];
        } catch (error) {
            console.error('获取MCP工具列表失败:', error);
            throw error;
        }
    }
}
