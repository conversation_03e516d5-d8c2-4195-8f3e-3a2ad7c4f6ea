import * as fs from 'fs';
import * as path from 'path';
import { watch, FSWatcher } from 'chokidar';

export enum DocumentStatus {
    COMPLETE = 'complete',           // 三个文档都存在
    MISSING_STEPS = 'missing_steps', // 缺少步骤文档
    MISSING_RESULT = 'missing_result' // 缺少结果文档
}

export interface TestDocumentGroup {
    name: string;
    testDoc?: string;      // *.md
    stepDoc?: string;      // *.yaml
    resultDoc?: string;    // *.result.md
    status: DocumentStatus;
}

export class DocumentScanner {
    private static instance: DocumentScanner;
    private documentGroups: TestDocumentGroup[] = [];
    private watcher: FSWatcher | null = null;
    private onDocumentsChangedCallback?: (groups: TestDocumentGroup[]) => void;
    private refreshTimeout: NodeJS.Timeout | null = null;
    private watchedPath: string | null = null;

    public static getInstance(): DocumentScanner {
        if (!DocumentScanner.instance) {
            DocumentScanner.instance = new DocumentScanner();
        }
        return DocumentScanner.instance;
    }

    public async scanDirectory(directoryPath: string): Promise<TestDocumentGroup[]> {
        if (!fs.existsSync(directoryPath)) {
            throw new Error(`目录不存在: ${directoryPath}`);
        }

        const groups = new Map<string, TestDocumentGroup>();
        
        await this.scanDirectoryRecursive(directoryPath, groups);
        
        this.documentGroups = Array.from(groups.values());
        
        // 触发文档变化回调
        if (this.onDocumentsChangedCallback) {
            this.onDocumentsChangedCallback(this.documentGroups);
        }
        
        return this.documentGroups;
    }

    private async scanDirectoryRecursive(dirPath: string, groups: Map<string, TestDocumentGroup>): Promise<void> {
        const entries = fs.readdirSync(dirPath, { withFileTypes: true });

        for (const entry of entries) {
            const fullPath = path.join(dirPath, entry.name);

            if (entry.isDirectory()) {
                await this.scanDirectoryRecursive(fullPath, groups);
            } else if (entry.isFile()) {
                this.processFile(fullPath, groups);
            }
        }
    }

    private processFile(filePath: string, groups: Map<string, TestDocumentGroup>): void {
        const fileName = path.basename(filePath);
        const ext = path.extname(fileName);
        
        let baseName: string | null = null;
        let fileType: 'test' | 'step' | 'result' | null = null;

        if (fileName.endsWith('.result.md')) {
            baseName = fileName.replace('.result.md', '');
            fileType = 'result';
        } else if (ext === '.md') {
            baseName = fileName.replace('.md', '');
            fileType = 'test';
        } else if (ext === '.yaml' || ext === '.yml') {
            baseName = fileName.replace(ext, '');
            fileType = 'step';
        }

        if (fileType && baseName) {
            
            if (!groups.has(baseName)) {
                groups.set(baseName, {
                    name: baseName,
                    status: DocumentStatus.MISSING_STEPS
                });
            }

            const group = groups.get(baseName)!;
            
            switch (fileType) {
                case 'test':
                    group.testDoc = filePath;
                    break;
                case 'step':
                    group.stepDoc = filePath;
                    break;
                case 'result':
                    group.resultDoc = filePath;
                    break;
            }

            // 更新状态
            this.updateGroupStatus(group);
        }
    }

    private updateGroupStatus(group: TestDocumentGroup): void {
        if (group.testDoc && group.stepDoc && group.resultDoc) {
            group.status = DocumentStatus.COMPLETE;
        } else if (group.testDoc && group.stepDoc) {
            group.status = DocumentStatus.MISSING_RESULT;
        } else {
            group.status = DocumentStatus.MISSING_STEPS;
        }
    }

    public getDocumentGroups(): TestDocumentGroup[] {
        return this.documentGroups;
    }

    public watchDirectory(directoryPath: string): void {
        if (this.watcher) {
            this.watcher.close();
        }

        this.watchedPath = directoryPath;
        this.watcher = watch(directoryPath, {
            ignored: /node_modules/,
            persistent: true,
            ignoreInitial: true,
            depth: 10, // 限制递归深度
            awaitWriteFinish: {
                stabilityThreshold: 100,
                pollInterval: 50
            }
        });

        this.watcher.on('add', () => this.refreshDocuments(directoryPath));
        this.watcher.on('unlink', () => this.refreshDocuments(directoryPath));
        this.watcher.on('change', () => this.refreshDocuments(directoryPath));
        this.watcher.on('unlinkDir', () => this.refreshDocuments(directoryPath));
        this.watcher.on('error', (error) => {
            console.error('文件监听器错误:', error);
        });
    }

    public onDocumentsChanged(callback: (groups: TestDocumentGroup[]) => void): void {
        this.onDocumentsChangedCallback = callback;
    }

    public async refreshDocumentsManually(directoryPath: string): Promise<TestDocumentGroup[]> {
        // 取消任何待处理的自动刷新
        if (this.refreshTimeout) {
            clearTimeout(this.refreshTimeout);
            this.refreshTimeout = null;
        }
        
        try {
            const groups = await this.scanDirectory(directoryPath);
            
            if (this.onDocumentsChangedCallback) {
                this.onDocumentsChangedCallback(this.documentGroups);
            }
            
            return groups;
        } catch (error) {
            console.error('手动刷新文档时出错:', error);
            throw error;
        }
    }

    public isWatching(): boolean {
        return this.watcher !== null;
    }

    public getWatchedPath(): string | null {
        return this.watchedPath;
    }

    private async refreshDocuments(directoryPath: string): Promise<void> {
        // 防抖：如果在短时间内有多个文件变化，只执行最后一次刷新
        if (this.refreshTimeout) {
            clearTimeout(this.refreshTimeout);
        }

        this.refreshTimeout = setTimeout(async () => {
            try {
                await this.scanDirectory(directoryPath);
                
                if (this.onDocumentsChangedCallback) {
                    this.onDocumentsChangedCallback(this.documentGroups);
                }
            } catch (error) {
                console.error('刷新文档时出错:', error);
            }
        }, 500); // 500ms 防抖延迟
    }

    public dispose(): void {
        if (this.refreshTimeout) {
            clearTimeout(this.refreshTimeout);
            this.refreshTimeout = null;
        }
        
        if (this.watcher) {
            this.watcher.close();
            this.watcher = null;
        }
        
        this.watchedPath = null;
    }
}