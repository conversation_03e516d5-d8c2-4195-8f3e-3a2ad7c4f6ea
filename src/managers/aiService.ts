import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import { AIConfig } from './config';

/**
 * AI服务响应接口
 */
export interface AIResponse {
    content: string;
    usage?: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
}

/**
 * AI服务错误类型
 */
export enum AIErrorType {
    NETWORK_ERROR = 'network_error',
    AUTH_ERROR = 'auth_error',
    RATE_LIMIT_ERROR = 'rate_limit_error',
    INVALID_REQUEST = 'invalid_request',
    SERVER_ERROR = 'server_error',
    UNKNOWN_ERROR = 'unknown_error'
}

/**
 * AI服务错误类
 */
export class AIServiceError extends Error {
    public readonly type: AIErrorType;
    public readonly statusCode?: number;
    public readonly originalError?: any;

    constructor(message: string, type: AIErrorType, statusCode?: number, originalError?: any) {
        super(message);
        this.name = 'AIServiceError';
        this.type = type;
        this.statusCode = statusCode;
        this.originalError = originalError;
    }
}

/**
 * OpenAI兼容的API请求格式
 */
interface ChatCompletionRequest {
    model: string;
    messages: Array<{
        role: 'system' | 'user' | 'assistant';
        content: string;
    }>;
    temperature?: number;
    max_tokens?: number;
    top_p?: number;
    frequency_penalty?: number;
    presence_penalty?: number;
    stream?: boolean;
    enable_thinking?: boolean;
}

/**
 * OpenAI兼容的API响应格式
 */
interface ChatCompletionResponse {
    id: string;
    object: string;
    created: number;
    model: string;
    choices: Array<{
        index: number;
        message: {
            role: string;
            content: string;
        };
        finish_reason: string;
    }>;
    usage?: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
}

/**
 * AI服务客户端类
 * 实现OpenAI兼容的API调用，支持HTTP请求封装、错误处理、API密钥认证
 */
export class AIServiceClient {
    private static instance: AIServiceClient;
    private httpClient: AxiosInstance;
    private config: AIConfig | null = null;

    private constructor() {
        // 创建axios实例，配置默认设置
        this.httpClient = axios.create({
            timeout: 60000, // 60秒超时
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'AI-Test-VSCode-Extension/1.0.0'
            }
        });

        // 设置请求拦截器，添加认证头
        this.httpClient.interceptors.request.use(
            (config) => {
                if (this.config?.apiKey) {
                    config.headers.Authorization = `Bearer ${this.config.apiKey}`;
                }
                return config;
            },
            (error) => {
                return Promise.reject(error);
            }
        );

        // 设置响应拦截器，统一错误处理
        this.httpClient.interceptors.response.use(
            (response) => response,
            (error) => {
                throw this.handleHttpError(error);
            }
        );
    }

    /**
     * 获取单例实例
     */
    public static getInstance(): AIServiceClient {
        if (!AIServiceClient.instance) {
            AIServiceClient.instance = new AIServiceClient();
        }
        return AIServiceClient.instance;
    }

    /**
     * 配置AI服务参数
     * @param config AI服务配置
     */
    public configure(config: AIConfig): void {
        this.config = { ...config };

        // 更新axios实例的baseURL
        this.httpClient.defaults.baseURL = this.normalizeApiUrl(config.apiUrl);

        console.log(`AI服务已配置: ${config.apiUrl}, 模型: ${config.model}`);
    }

    /**
     * 创建标准的请求参数
     * @param messages 消息数组
     * @param options 可选参数
     * @returns 请求对象
     */
    private createRequest(
        messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }>,
        options: {
            temperature?: number;
            max_tokens?: number;
        } = {}
    ): ChatCompletionRequest {
        return {
            model: this.config!.model,
            messages,
            temperature: options.temperature ?? 0.3,
            max_tokens: options.max_tokens ?? 2000,
            stream: false,
            enable_thinking: false
        };
    }

    /**
     * 测试AI服务连接
     * @returns 连接是否成功
     */
    public async testConnection(): Promise<boolean> {
        if (!this.config) {
            throw new AIServiceError('AI服务未配置', AIErrorType.INVALID_REQUEST);
        }

        try {
            // 发送一个简单的测试请求
            const testRequest = this.createRequest(
                [
                    {
                        role: 'user',
                        content: 'Hello, this is a connection test.'
                    }
                ],
                {
                    max_tokens: 10,
                    temperature: 0.1
                }
            );

            const response = await this.httpClient.post<ChatCompletionResponse>(
                '/chat/completions',
                testRequest
            );

            return response.status === 200 && response.data.choices?.length > 0;
        } catch (error) {
            console.error('AI服务连接测试失败:', error);
            return false;
        }
    }

    /**
     * 生成测试步骤
     * @param testContent 测试文档内容
     * @returns 生成的步骤内容
     */
    public async generateSteps(testContent: string): Promise<string> {
        if (!this.config) {
            throw new AIServiceError('AI服务未配置', AIErrorType.INVALID_REQUEST);
        }

        if (!testContent.trim()) {
            throw new AIServiceError('测试内容不能为空', AIErrorType.INVALID_REQUEST);
        }

        try {
            const request = this.createRequest(
                [
                    {
                        role: 'system',
                        content: this.getSystemPrompt()
                    },
                    {
                        role: 'user',
                        content: this.buildUserPrompt(testContent)
                    }
                ],
                {
                    temperature: 0.3,
                    max_tokens: 2000
                }
            );

            console.log('发送AI请求:', {
                model: request.model,
                messagesCount: request.messages.length,
                contentLength: testContent.length,
                apiUrl: this.httpClient.defaults.baseURL,
                requestParams: {
                    temperature: request.temperature,
                    max_tokens: request.max_tokens,
                    stream: request.stream,
                    enable_thinking: request.enable_thinking
                }
            });

            const response = await this.httpClient.post<ChatCompletionResponse>(
                '/chat/completions',
                request
            );

            if (!response.data.choices || response.data.choices.length === 0) {
                throw new AIServiceError('AI服务返回空响应', AIErrorType.SERVER_ERROR);
            }

            const generatedContent = response.data.choices[0].message.content;
            
            console.log('AI响应成功:', {
                usage: response.data.usage,
                contentLength: generatedContent.length
            });

            return generatedContent;
        } catch (error) {
            if (error instanceof AIServiceError) {
                throw error;
            }
            throw this.handleHttpError(error);
        }
    }

    /**
     * 规范化API URL
     * @param apiUrl 原始API URL
     * @returns 规范化后的URL
     */
    private normalizeApiUrl(apiUrl: string): string {
        if (!apiUrl) {
            return '';
        }

        // 移除末尾的斜杠
        let normalizedUrl = apiUrl.replace(/\/+$/, '');
        
        // 如果URL不包含/v1路径，自动添加
        if (!normalizedUrl.includes('/v1')) {
            normalizedUrl += '/v1';
        }

        return normalizedUrl;
    }

    /**
     * 获取系统提示词
     * @returns 系统提示词
     */
    private getSystemPrompt(): string {
        return `你是一个专业的测试自动化专家，负责将自然语言的测试描述转换为结构化的YAML格式测试步骤。

请根据用户提供的测试文档内容，生成符合以下格式的YAML测试步骤：

\`\`\`yaml
name: "测试名称"
description: "测试描述"
target: "目标网站URL"
steps:
  - goal: "步骤目标描述"
    operation: "操作类型"
    content: "操作内容"
\`\`\`

操作类型包括：
- "导航": 访问网页或URL
- "点击": 点击按钮、链接或元素
- "填写": 输入文本到表单字段
- "检查": 验证页面内容或状态
- "等待": 等待页面加载或元素出现
- "截图": 捕获页面截图

要求：
1. 仔细分析测试文档，理解测试目标和流程
2. 将测试步骤分解为清晰、可执行的操作
3. 确保每个步骤都有明确的目标和操作类型
4. 生成的YAML格式必须正确且完整
5. 只返回YAML内容，不要包含其他解释文字`;
    }

    /**
     * 构建用户提示词
     * @param testContent 测试文档内容
     * @returns 用户提示词
     */
    private buildUserPrompt(testContent: string): string {
        return `请为以下测试文档生成YAML格式的测试步骤：

${testContent}

请生成完整的YAML测试步骤文档。`;
    }

    /**
     * 处理HTTP错误
     * @param error axios错误对象
     * @returns AIServiceError
     */
    private handleHttpError(error: any): AIServiceError {
        if (axios.isAxiosError(error)) {
            const axiosError = error as AxiosError;
            
            if (!axiosError.response) {
                // 网络错误
                return new AIServiceError(
                    '网络连接失败，请检查网络设置和API地址',
                    AIErrorType.NETWORK_ERROR,
                    undefined,
                    error
                );
            }

            const statusCode = axiosError.response.status;
            const responseData = axiosError.response.data as any;
            
            switch (statusCode) {
                case 401:
                    return new AIServiceError(
                        'API密钥无效或已过期',
                        AIErrorType.AUTH_ERROR,
                        statusCode,
                        error
                    );
                case 429:
                    return new AIServiceError(
                        'API调用频率超限，请稍后重试',
                        AIErrorType.RATE_LIMIT_ERROR,
                        statusCode,
                        error
                    );
                case 400:
                    const errorMessage = responseData?.error?.message || responseData?.message || '未知错误';
                    return new AIServiceError(
                        `请求参数错误: ${errorMessage}`,
                        AIErrorType.INVALID_REQUEST,
                        statusCode,
                        error
                    );
                case 500:
                case 502:
                case 503:
                case 504:
                    return new AIServiceError(
                        'AI服务暂时不可用，请稍后重试',
                        AIErrorType.SERVER_ERROR,
                        statusCode,
                        error
                    );
                default:
                    return new AIServiceError(
                        `AI服务错误 (${statusCode}): ${responseData?.error?.message || '未知错误'}`,
                        AIErrorType.UNKNOWN_ERROR,
                        statusCode,
                        error
                    );
            }
        }

        return new AIServiceError(
            `未知错误: ${error.message || '请求失败'}`,
            AIErrorType.UNKNOWN_ERROR,
            undefined,
            error
        );
    }
}
