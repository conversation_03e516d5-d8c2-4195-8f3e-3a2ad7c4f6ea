import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { TestResult, StepResult } from './testExecutor';

/**
 * 测试报告配置
 */
export interface ReportConfig {
    includeScreenshots: boolean;    // 是否包含截图
    includeTimestamps: boolean;     // 是否包含时间戳
    includeDetails: boolean;        // 是否包含详细信息
    outputDirectory: string;        // 输出目录
    templatePath?: string;          // 自定义模板路径
}

/**
 * 错误信息结构
 */
export interface ErrorInfo {
    stepIndex: number;              // 出错步骤索引
    errorType: string;              // 错误类型
    errorMessage: string;           // 错误消息
    stackTrace?: string;            // 堆栈跟踪
    screenshot?: string;            // 错误截图
    timestamp: Date;                // 错误时间
}

/**
 * 测试结果生成器类
 * 负责生成Markdown格式的测试结果报告，收集截图和错误信息
 */
export class ResultGenerator {
    private static instance: ResultGenerator;
    private config: ReportConfig;

    private constructor() {
        // 默认配置
        this.config = {
            includeScreenshots: true,
            includeTimestamps: true,
            includeDetails: true,
            outputDirectory: ''
        };
    }

    /**
     * 获取单例实例
     */
    public static getInstance(): ResultGenerator {
        if (!ResultGenerator.instance) {
            ResultGenerator.instance = new ResultGenerator();
        }
        return ResultGenerator.instance;
    }

    /**
     * 设置报告配置
     */
    public setConfig(config: Partial<ReportConfig>): void {
        this.config = { ...this.config, ...config };
    }

    /**
     * 获取当前配置
     */
    public getConfig(): ReportConfig {
        return { ...this.config };
    }

    /**
     * 生成测试结果报告
     */
    public async generateReport(testResult: TestResult, outputPath?: string): Promise<string> {
        try {
            console.log(`生成测试报告: ${testResult.testName}`);

            // 确定输出路径
            const reportPath = outputPath || this.getDefaultReportPath(testResult.testName);
            
            // 生成Markdown内容
            const markdownContent = this.generateMarkdownContent(testResult);
            
            // 确保输出目录存在
            const outputDir = path.dirname(reportPath);
            if (!fs.existsSync(outputDir)) {
                fs.mkdirSync(outputDir, { recursive: true });
            }

            // 写入文件
            fs.writeFileSync(reportPath, markdownContent, 'utf-8');

            // 如果测试失败，生成错误报告
            if (testResult.status === 'failed') {
                await this.generateErrorReport(testResult, reportPath);
            }

            console.log(`测试报告已生成: ${reportPath}`);
            return reportPath;

        } catch (error) {
            console.error('生成测试报告失败:', error);
            throw error;
        }
    }

    /**
     * 生成Markdown内容
     */
    private generateMarkdownContent(testResult: TestResult): string {
        const lines: string[] = [];

        // 标题
        lines.push(`# 测试结果报告`);
        lines.push('');

        // 基本信息
        lines.push(`**测试名称**: ${testResult.testName}`);
        
        if (this.config.includeTimestamps) {
            lines.push(`**执行时间**: ${this.formatDateTime(testResult.startTime)}`);
            lines.push(`**完成时间**: ${this.formatDateTime(testResult.endTime)}`);
        }
        
        lines.push(`**执行时长**: ${this.formatDuration(testResult.duration)}`);
        lines.push(`**状态**: ${this.getStatusEmoji(testResult.status)} ${this.getStatusText(testResult.status)}`);
        lines.push('');

        // 整体错误信息
        if (testResult.error) {
            lines.push(`**错误信息**: ${testResult.error}`);
            lines.push('');
        }

        // 执行摘要
        lines.push('## 执行摘要');
        lines.push('');
        
        const totalSteps = testResult.steps.length;
        const successSteps = testResult.steps.filter(s => s.status === 'success').length;
        const failedSteps = testResult.steps.filter(s => s.status === 'failed').length;
        const skippedSteps = testResult.steps.filter(s => s.status === 'skipped').length;

        lines.push(`- **总步骤数**: ${totalSteps}`);
        lines.push(`- **成功步骤**: ${successSteps}`);
        lines.push(`- **失败步骤**: ${failedSteps}`);
        if (skippedSteps > 0) {
            lines.push(`- **跳过步骤**: ${skippedSteps}`);
        }
        lines.push('');

        // 步骤执行详情
        if (testResult.steps.length > 0) {
            lines.push('## 步骤执行详情');
            lines.push('');

            testResult.steps.forEach((step, index) => {
                lines.push(`### 步骤 ${index + 1}: ${step.goal}`);
                lines.push('');
                
                lines.push(`- **状态**: ${this.getStatusEmoji(step.status)} ${this.getStatusText(step.status)}`);
                lines.push(`- **耗时**: ${this.formatDuration(step.duration)}`);
                
                if (this.config.includeDetails && step.details) {
                    lines.push(`- **详情**: ${step.details}`);
                }
                
                if (step.error) {
                    lines.push(`- **错误**: ${step.error}`);
                }
                
                if (this.config.includeScreenshots && step.screenshot) {
                    const screenshotName = path.basename(step.screenshot);
                    lines.push(`- **截图**: ![步骤${index + 1}截图](${screenshotName})`);
                }
                
                lines.push('');
            });
        }

        // 截图汇总
        if (this.config.includeScreenshots && testResult.screenshots.length > 0) {
            lines.push('## 截图汇总');
            lines.push('');
            
            testResult.screenshots.forEach((screenshot, index) => {
                const screenshotName = path.basename(screenshot);
                lines.push(`### 截图 ${index + 1}`);
                lines.push(`![截图${index + 1}](${screenshotName})`);
                lines.push('');
            });
        }

        // 生成信息
        lines.push('---');
        lines.push('');
        lines.push(`*报告生成时间: ${this.formatDateTime(new Date())}*`);
        lines.push(`*生成工具: AI自动化测试插件 v1.0.0*`);

        return lines.join('\n');
    }

    /**
     * 生成错误报告
     */
    private async generateErrorReport(testResult: TestResult, reportPath: string): Promise<void> {
        try {
            const errorReportPath = this.getErrorReportPath(testResult.testName, reportPath);
            const errorInfo = this.extractErrorInfo(testResult);
            
            const errorContent = this.generateErrorMarkdown(testResult, errorInfo);
            
            fs.writeFileSync(errorReportPath, errorContent, 'utf-8');
            console.log(`错误报告已生成: ${errorReportPath}`);

        } catch (error) {
            console.error('生成错误报告失败:', error);
        }
    }

    /**
     * 提取错误信息
     */
    private extractErrorInfo(testResult: TestResult): ErrorInfo[] {
        const errors: ErrorInfo[] = [];

        // 整体错误
        if (testResult.error) {
            errors.push({
                stepIndex: -1,
                errorType: 'TestExecutionError',
                errorMessage: testResult.error,
                timestamp: testResult.endTime
            });
        }

        // 步骤错误
        testResult.steps.forEach((step, index) => {
            if (step.status === 'failed' && step.error) {
                errors.push({
                    stepIndex: index,
                    errorType: 'StepExecutionError',
                    errorMessage: step.error,
                    screenshot: step.screenshot,
                    timestamp: new Date(testResult.startTime.getTime() + 
                        testResult.steps.slice(0, index + 1).reduce((sum, s) => sum + s.duration, 0))
                });
            }
        });

        return errors;
    }

    /**
     * 生成错误Markdown内容
     */
    private generateErrorMarkdown(testResult: TestResult, errors: ErrorInfo[]): string {
        const lines: string[] = [];

        lines.push(`# 错误报告 - ${testResult.testName}`);
        lines.push('');
        lines.push(`**生成时间**: ${this.formatDateTime(new Date())}`);
        lines.push(`**测试状态**: ${testResult.status}`);
        lines.push(`**错误数量**: ${errors.length}`);
        lines.push('');

        errors.forEach((error, index) => {
            lines.push(`## 错误 ${index + 1}`);
            lines.push('');
            
            if (error.stepIndex >= 0) {
                lines.push(`**步骤**: ${error.stepIndex + 1} - ${testResult.steps[error.stepIndex]?.goal || '未知步骤'}`);
            } else {
                lines.push(`**类型**: 整体测试错误`);
            }
            
            lines.push(`**错误类型**: ${error.errorType}`);
            lines.push(`**错误时间**: ${this.formatDateTime(error.timestamp)}`);
            lines.push(`**错误信息**:`);
            lines.push('```');
            lines.push(error.errorMessage);
            lines.push('```');
            
            if (error.stackTrace) {
                lines.push(`**堆栈跟踪**:`);
                lines.push('```');
                lines.push(error.stackTrace);
                lines.push('```');
            }
            
            if (error.screenshot) {
                lines.push(`**错误截图**: ![错误截图](${path.basename(error.screenshot)})`);
            }
            
            lines.push('');
        });

        return lines.join('\n');
    }

    /**
     * 获取默认报告路径
     */
    private getDefaultReportPath(testName: string): string {
        const outputDir = this.config.outputDirectory || process.cwd();
        return path.join(outputDir, `${testName}.result.md`);
    }

    /**
     * 获取错误报告路径
     */
    private getErrorReportPath(testName: string, reportPath: string): string {
        const dir = path.dirname(reportPath);
        const timestamp = new Date().toISOString().slice(0, 10); // YYYY-MM-DD
        return path.join(dir, `${testName}.${timestamp}.error.md`);
    }

    /**
     * 格式化日期时间
     */
    private formatDateTime(date: Date): string {
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    /**
     * 格式化持续时间
     */
    private formatDuration(milliseconds: number): string {
        if (milliseconds < 1000) {
            return `${milliseconds}ms`;
        }
        
        const seconds = Math.floor(milliseconds / 1000);
        const ms = milliseconds % 1000;
        
        if (seconds < 60) {
            return `${seconds}.${Math.floor(ms / 100)}s`;
        }
        
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        
        return `${minutes}m ${remainingSeconds}s`;
    }

    /**
     * 获取状态表情符号
     */
    private getStatusEmoji(status: string): string {
        switch (status) {
            case 'success': return '✅';
            case 'failed': return '❌';
            case 'cancelled': return '⏹️';
            case 'skipped': return '⏭️';
            default: return '❓';
        }
    }

    /**
     * 获取状态文本
     */
    private getStatusText(status: string): string {
        switch (status) {
            case 'success': return '成功';
            case 'failed': return '失败';
            case 'cancelled': return '已取消';
            case 'skipped': return '已跳过';
            default: return '未知';
        }
    }

    /**
     * 批量生成报告
     */
    public async generateBatchReports(testResults: TestResult[], outputDirectory?: string): Promise<string[]> {
        const reportPaths: string[] = [];
        
        for (const result of testResults) {
            try {
                const outputPath = outputDirectory ? 
                    path.join(outputDirectory, `${result.testName}.result.md`) : 
                    undefined;
                
                const reportPath = await this.generateReport(result, outputPath);
                reportPaths.push(reportPath);
                
            } catch (error) {
                console.error(`生成报告失败 (${result.testName}):`, error);
            }
        }
        
        return reportPaths;
    }

    /**
     * 生成汇总报告
     */
    public async generateSummaryReport(testResults: TestResult[], outputPath?: string): Promise<string> {
        try {
            const summaryPath = outputPath || path.join(this.config.outputDirectory || process.cwd(), 'test-summary.md');
            const summaryContent = this.generateSummaryMarkdown(testResults);
            
            fs.writeFileSync(summaryPath, summaryContent, 'utf-8');
            console.log(`汇总报告已生成: ${summaryPath}`);
            
            return summaryPath;
            
        } catch (error) {
            console.error('生成汇总报告失败:', error);
            throw error;
        }
    }

    /**
     * 生成汇总Markdown内容
     */
    private generateSummaryMarkdown(testResults: TestResult[]): string {
        const lines: string[] = [];

        lines.push('# 测试执行汇总报告');
        lines.push('');
        lines.push(`**生成时间**: ${this.formatDateTime(new Date())}`);
        lines.push(`**测试总数**: ${testResults.length}`);
        lines.push('');

        // 统计信息
        const successCount = testResults.filter(r => r.status === 'success').length;
        const failedCount = testResults.filter(r => r.status === 'failed').length;
        const cancelledCount = testResults.filter(r => r.status === 'cancelled').length;
        const totalDuration = testResults.reduce((sum, r) => sum + r.duration, 0);

        lines.push('## 执行统计');
        lines.push('');
        lines.push(`- **成功**: ${successCount} (${((successCount / testResults.length) * 100).toFixed(1)}%)`);
        lines.push(`- **失败**: ${failedCount} (${((failedCount / testResults.length) * 100).toFixed(1)}%)`);
        if (cancelledCount > 0) {
            lines.push(`- **取消**: ${cancelledCount} (${((cancelledCount / testResults.length) * 100).toFixed(1)}%)`);
        }
        lines.push(`- **总耗时**: ${this.formatDuration(totalDuration)}`);
        lines.push('');

        // 测试详情表格
        lines.push('## 测试详情');
        lines.push('');
        lines.push('| 测试名称 | 状态 | 耗时 | 步骤数 | 成功步骤 | 失败步骤 |');
        lines.push('|---------|------|------|--------|----------|----------|');

        testResults.forEach(result => {
            const successSteps = result.steps.filter(s => s.status === 'success').length;
            const failedSteps = result.steps.filter(s => s.status === 'failed').length;
            
            lines.push(`| ${result.testName} | ${this.getStatusEmoji(result.status)} ${this.getStatusText(result.status)} | ${this.formatDuration(result.duration)} | ${result.steps.length} | ${successSteps} | ${failedSteps} |`);
        });

        lines.push('');

        // 失败测试详情
        const failedTests = testResults.filter(r => r.status === 'failed');
        if (failedTests.length > 0) {
            lines.push('## 失败测试详情');
            lines.push('');

            failedTests.forEach(result => {
                lines.push(`### ${result.testName}`);
                lines.push('');
                if (result.error) {
                    lines.push(`**错误**: ${result.error}`);
                }
                
                const failedSteps = result.steps.filter(s => s.status === 'failed');
                if (failedSteps.length > 0) {
                    lines.push(`**失败步骤**:`);
                    failedSteps.forEach(step => {
                        lines.push(`- 步骤 ${step.stepIndex + 1}: ${step.goal} - ${step.error || '未知错误'}`);
                    });
                }
                lines.push('');
            });
        }

        return lines.join('\n');
    }
}
