(function() {
    const vscode = acquireVsCodeApi();
    
    // 状态管理
    let currentTestGroups = [];
    let selectedTests = new Set();
    
    // 初始化
    document.addEventListener('DOMContentLoaded', () => {
        initializeUI();
        loadConfiguration();
    });
    
    function initializeUI() {
        // 标签页切换
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.getAttribute('data-tab');
                switchTab(tabName);
            });
        });
        
        // 绑定事件监听器
        bindEventListeners();
    }
    
    function switchTab(tabName) {
        // 更新按钮状态
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        // 更新内容显示
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');
        
        // 保存当前标签页状态
        vscode.setState({ ...vscode.getState(), activeTab: tabName });
    }
    
    function bindEventListeners() {
        // 测试标签页事件
        bindTestTabEvents();
        
        // 配置标签页事件
        bindConfigTabEvents();
        
        // 通用事件
        bindCommonEvents();
    }
    
    function bindTestTabEvents() {
        // 扫描文档
        document.getElementById('scanBtn').addEventListener('click', () => {
            showLoading('正在扫描文档...');
            vscode.postMessage({
                command: 'scanDocuments'
            });
            addStatusEntry('开始扫描测试文档...', 'info');
        });
        
        // 全选/取消全选
        document.getElementById('selectAllBtn').addEventListener('click', () => {
            const checkboxes = document.querySelectorAll('.test-checkbox');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);
            
            checkboxes.forEach(cb => {
                cb.checked = !allChecked;
                const testName = cb.getAttribute('data-test-name');
                if (cb.checked) {
                    selectedTests.add(testName);
                } else {
                    selectedTests.delete(testName);
                }
            });
            
            updateExecuteButtonState();
            updateSelectAllButtonText();
        });
        
        // 执行测试
        document.getElementById('executeBtn').addEventListener('click', () => {
            if (selectedTests.size === 0) {
                showNotification('请选择要执行的测试', 'warning');
                return;
            }
            
            const testNames = Array.from(selectedTests);
            showLoading(`正在执行 ${testNames.length} 个测试...`);
            
            vscode.postMessage({
                command: 'executeTests',
                testNames: testNames
            });
            
            addStatusEntry(`开始执行 ${testNames.length} 个测试`, 'info');
        });
        
        // 清空状态
        document.getElementById('clearStatusBtn').addEventListener('click', () => {
            clearStatusArea();
        });
        
        // 生成步骤
        document.getElementById('generateBtn').addEventListener('click', () => {
            const selectedTestNames = Array.from(selectedTests);
            if (selectedTestNames.length === 0) {
                showNotification('请选择要生成步骤的测试', 'warning');
                return;
            }
            
            showLoading(`正在为 ${selectedTestNames.length} 个测试生成步骤...`);
            
            selectedTestNames.forEach(testName => {
                vscode.postMessage({
                    command: 'generateSteps',
                    testName: testName
                });
            });
            
            addStatusEntry(`开始为 ${selectedTestNames.length} 个测试生成步骤`, 'info');
        });
        
        // 导出状态
        document.getElementById('exportStatusBtn').addEventListener('click', () => {
            const statusEntries = document.querySelectorAll('.status-entry');
            if (statusEntries.length === 0) {
                showNotification('没有状态信息可导出', 'warning');
                return;
            }
            
            const statusData = Array.from(statusEntries).map(entry => {
                const timestamp = entry.querySelector('.status-timestamp').textContent;
                const message = entry.querySelector('.status-message').textContent;
                return `[${timestamp}] ${message}`;
            }).join('\n');
            
            vscode.postMessage({
                command: 'exportStatus',
                data: statusData
            });
        });
    }
    
    function bindConfigTabEvents() {
        // 保存配置
        document.getElementById('saveConfigBtn').addEventListener('click', () => {
            const config = {
                ai: {
                    apiUrl: document.getElementById('apiUrl').value.trim(),
                    apiKey: document.getElementById('apiKey').value.trim(),
                    model: document.getElementById('model').value.trim()
                },
                testDirectory: document.getElementById('testDirectory').value.trim()
            };
            
            // 验证配置
            if (!config.ai.apiUrl || !config.ai.apiKey || !config.testDirectory) {
                showNotification('请填写完整的配置信息', 'warning');
                return;
            }
            
            showLoading('正在保存配置...');
            vscode.postMessage({
                command: 'saveConfig',
                config: config
            });
        });
        
        // 测试连接
        document.getElementById('testConnectionBtn').addEventListener('click', () => {
            const apiUrl = document.getElementById('apiUrl').value.trim();
            const apiKey = document.getElementById('apiKey').value.trim();
            const model = document.getElementById('model').value.trim();
            
            if (!apiUrl || !apiKey) {
                showNotification('请先填写API地址和密钥', 'warning');
                return;
            }
            
            showLoading('正在测试AI服务连接...');
            vscode.postMessage({
                command: 'testConnection',
                config: { apiUrl, apiKey, model }
            });
        });
        
        // 浏览目录
        document.getElementById('browseBtn').addEventListener('click', () => {
            vscode.postMessage({
                command: 'browseDirectory'
            });
        });
        
        // 重置配置
        document.getElementById('resetConfigBtn').addEventListener('click', () => {
            if (confirm('确定要重置所有配置吗？此操作不可撤销。')) {
                document.getElementById('apiUrl').value = '';
                document.getElementById('apiKey').value = '';
                document.getElementById('model').value = 'gpt-3.5-turbo';
                document.getElementById('testDirectory').value = '';
                
                updateConfigStatus();
                showNotification('配置已重置', 'info');
            }
        });
        
        // 验证配置
        document.getElementById('validateConfigBtn').addEventListener('click', () => {
            const apiUrl = document.getElementById('apiUrl').value.trim();
            const apiKey = document.getElementById('apiKey').value.trim();
            const testDirectory = document.getElementById('testDirectory').value.trim();
            
            let isValid = true;
            let errors = [];
            
            if (!apiUrl) {
                errors.push('API地址不能为空');
                isValid = false;
            }
            
            if (!apiKey) {
                errors.push('API密钥不能为空');
                isValid = false;
            }
            
            if (!testDirectory) {
                errors.push('测试目录不能为空');
                isValid = false;
            }
            
            if (isValid) {
                showNotification('配置验证通过！', 'success');
                updateConfigStatus();
            } else {
                showNotification(`配置验证失败：${errors.join(', ')}`, 'error');
            }
        });
        
        // 配置输入变化监听
        ['apiUrl', 'apiKey', 'model', 'testDirectory'].forEach(id => {
            document.getElementById(id).addEventListener('input', () => {
                updateConfigStatus();
            });
        });
    }
    
    function bindCommonEvents() {
        // 处理来自扩展的消息
        window.addEventListener('message', event => {
            const message = event.data;
            hideLoading();
            
            switch (message.command) {
                case 'scanComplete':
                    handleScanComplete(message);
                    break;
                    
                case 'generateComplete':
                    handleGenerateComplete(message);
                    break;
                    
                case 'executeComplete':
                    handleExecuteComplete(message);
                    break;
                    
                case 'configSaved':
                    handleConfigSaved(message);
                    break;
                    
                case 'testConnection':
                    handleTestConnection(message);
                    break;
                    
                case 'browseDirectory':
                    handleBrowseDirectory(message);
                    break;
                    
                case 'loadConfig':
                    handleLoadConfig(message);
                    break;
                    
                case 'updateTestGroups':
                    handleUpdateTestGroups(message);
                    break;
            }
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 's':
                        e.preventDefault();
                        if (document.getElementById('config').classList.contains('active')) {
                            document.getElementById('saveConfigBtn').click();
                        }
                        break;
                    case 'r':
                        e.preventDefault();
                        document.getElementById('scanBtn').click();
                        break;
                }
            }
        });
    }
    
    // 消息处理函数
    function handleScanComplete(message) {
        if (message.success) {
            currentTestGroups = message.testGroups || [];
            updateTestList(currentTestGroups);
            addStatusEntry(`扫描完成，发现 ${currentTestGroups.length} 个测试文档组`, 'success');
            showNotification(`发现 ${currentTestGroups.length} 个测试文档组`, 'success');
        } else {
            addStatusEntry(`扫描失败: ${message.error}`, 'error');
            showNotification(`扫描失败: ${message.error}`, 'error');
        }
    }
    
    function handleGenerateComplete(message) {
        if (message.success) {
            addStatusEntry(`${message.testName} 步骤生成完成`, 'success');
            showNotification(`${message.testName} 步骤生成完成`, 'success');
            // 刷新测试列表
            vscode.postMessage({ command: 'scanDocuments' });
        } else {
            addStatusEntry(`${message.testName} 步骤生成失败: ${message.error}`, 'error');
            showNotification(`${message.testName} 步骤生成失败`, 'error');
        }
    }
    
    function handleExecuteComplete(message) {
        if (message.success) {
            addStatusEntry(`测试执行完成`, 'success');
            showNotification('测试执行完成', 'success');
        } else {
            addStatusEntry(`测试执行失败: ${message.error}`, 'error');
            showNotification('测试执行失败', 'error');
        }
    }
    
    function handleConfigSaved(message) {
        if (message.success) {
            showNotification('配置保存成功', 'success');
            updateConfigStatus();
        } else {
            showNotification(`配置保存失败: ${message.error}`, 'error');
        }
    }
    
    function handleTestConnection(message) {
        if (message.success) {
            showNotification('AI服务连接测试成功', 'success');
            updateConfigStatus();
        } else {
            showNotification(`连接测试失败: ${message.error}`, 'error');
        }
    }
    
    function handleBrowseDirectory(message) {
        if (message.success && message.directory) {
            document.getElementById('testDirectory').value = message.directory;
            updateConfigStatus();
        }
    }
    
    function handleLoadConfig(message) {
        if (message.config) {
            const config = message.config;
            document.getElementById('apiUrl').value = config.ai?.apiUrl || '';
            document.getElementById('apiKey').value = config.ai?.apiKey || '';
            document.getElementById('model').value = config.ai?.model || 'gpt-3.5-turbo';
            document.getElementById('testDirectory').value = config.testDirectory || '';
            updateConfigStatus();
        }
    }
    
    function handleUpdateTestGroups(message) {
        if (message.testGroups) {
            currentTestGroups = message.testGroups;
            updateTestList(currentTestGroups);
        }
    }
    
    // UI更新函数
    function updateTestList(testGroups) {
        const testList = document.getElementById('testList');
        
        if (!testGroups || testGroups.length === 0) {
            testList.innerHTML = `
                <div class="placeholder">
                    <div class="placeholder-icon">🔍</div>
                    <p>未发现测试文档</p>
                    <small>请检查目录配置或点击"扫描文档"重新扫描</small>
                </div>
            `;
            return;
        }
        
        const html = testGroups.map((group, index) => `
            <div class="test-item" data-test-name="${group.name}" style="animation-delay: ${index * 0.1}s">
                <input type="checkbox" class="test-checkbox" data-test-name="${group.name}">
                <div class="test-info">
                    <div class="test-name">${group.name}</div>
                    <div class="test-path">${group.testDoc || '无测试文档'}</div>
                </div>
                <span class="test-status status-${group.status}">${getStatusText(group.status)}</span>
                <div class="test-actions">
                    <button class="btn-mini" onclick="generateStepsForTest('${group.name}')" title="生成步骤">
                        ✨
                    </button>
                    <button class="btn-mini" onclick="executeTest('${group.name}')" title="执行测试">
                        ▶️
                    </button>
                </div>
            </div>
        `).join('');
        
        testList.innerHTML = html;
        
        // 绑定复选框事件
        testList.querySelectorAll('.test-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const testName = e.target.getAttribute('data-test-name');
                if (e.target.checked) {
                    selectedTests.add(testName);
                } else {
                    selectedTests.delete(testName);
                }
                updateExecuteButtonState();
                updateSelectAllButtonText();
            });
        });
        
        // 绑定测试项点击事件
        testList.querySelectorAll('.test-item').forEach(item => {
            item.addEventListener('click', (e) => {
                if (e.target.type !== 'checkbox') {
                    const checkbox = item.querySelector('.test-checkbox');
                    checkbox.click();
                }
            });
        });
    }
    
    function getStatusText(status) {
        switch (status) {
            case 'complete': return '完整';
            case 'missing_steps': return '缺少步骤';
            case 'missing_result': return '缺少结果';
            default: return '未知';
        }
    }
    
    function updateExecuteButtonState() {
        const executeBtn = document.getElementById('executeBtn');
        executeBtn.disabled = selectedTests.size === 0;
    }
    
    function updateSelectAllButtonText() {
        const selectAllBtn = document.getElementById('selectAllBtn');
        const checkboxes = document.querySelectorAll('.test-checkbox');
        const checkedCount = document.querySelectorAll('.test-checkbox:checked').length;
        
        if (checkedCount === 0) {
            selectAllBtn.innerHTML = '<span class="btn-icon">☑️</span>全选';
        } else if (checkedCount === checkboxes.length) {
            selectAllBtn.innerHTML = '<span class="btn-icon">☐</span>取消全选';
        } else {
            selectAllBtn.innerHTML = `<span class="btn-icon">☑️</span>全选 (${checkedCount}/${checkboxes.length})`;
        }
    }
    
    function updateConfigStatus() {
        const apiUrl = document.getElementById('apiUrl').value.trim();
        const apiKey = document.getElementById('apiKey').value.trim();
        const testDirectory = document.getElementById('testDirectory').value.trim();
        
        // 更新AI配置状态
        const aiStatus = document.getElementById('aiConfigStatus');
        const aiIndicator = aiStatus.querySelector('.status-indicator');
        const aiText = aiStatus.querySelector('.status-text');
        
        if (apiUrl && apiKey) {
            aiIndicator.className = 'status-indicator status-valid';
            aiText.textContent = '已配置';
        } else {
            aiIndicator.className = 'status-indicator status-invalid';
            aiText.textContent = '未完成';
        }
        
        // 更新目录配置状态
        const dirStatus = document.getElementById('dirConfigStatus');
        const dirIndicator = dirStatus.querySelector('.status-indicator');
        const dirText = dirStatus.querySelector('.status-text');
        
        if (testDirectory) {
            dirIndicator.className = 'status-indicator status-valid';
            dirText.textContent = '已配置';
        } else {
            dirIndicator.className = 'status-indicator status-invalid';
            dirText.textContent = '未配置';
        }
    }
    
    function addStatusEntry(message, type = 'info') {
        const statusArea = document.getElementById('statusArea');
        const timestamp = new Date().toLocaleTimeString();
        
        // 如果是第一条消息，清除占位符
        const placeholder = statusArea.querySelector('.placeholder');
        if (placeholder) {
            placeholder.remove();
        }
        
        const entry = document.createElement('div');
        entry.className = `status-entry status-${type}`;
        entry.innerHTML = `
            <div class="status-timestamp">${timestamp}</div>
            <div class="status-message">${message}</div>
        `;
        
        statusArea.insertBefore(entry, statusArea.firstChild);
        
        // 限制状态条目数量
        const entries = statusArea.querySelectorAll('.status-entry');
        if (entries.length > 50) {
            entries[entries.length - 1].remove();
        }
        
        // 滚动到顶部
        statusArea.scrollTop = 0;
    }
    
    function clearStatusArea() {
        const statusArea = document.getElementById('statusArea');
        statusArea.innerHTML = `
            <div class="placeholder">
                <div class="placeholder-icon">📊</div>
                <p>暂无执行状态</p>
            </div>
        `;
    }
    
    function showNotification(message, type = 'info', title = '') {
        const notifications = document.getElementById('notifications');
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        
        notification.innerHTML = `
            <div class="notification-icon">${icons[type] || icons.info}</div>
            <div class="notification-content">
                ${title ? `<div class="notification-title">${title}</div>` : ''}
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close">×</button>
        `;
        
        notifications.appendChild(notification);
        
        // 绑定关闭事件
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.remove();
        });
        
        // 自动关闭
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, type === 'error' ? 8000 : 5000);
    }
    
    function showLoading(message = '处理中...') {
        const overlay = document.getElementById('loadingOverlay');
        const text = overlay.querySelector('.loading-text');
        text.textContent = message;
        overlay.style.display = 'flex';
    }
    
    function hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        overlay.style.display = 'none';
    }
    
    function loadConfiguration() {
        vscode.postMessage({ command: 'loadConfig' });
    }
    
    // 全局函数
    window.generateStepsForTest = function(testName) {
        showLoading(`正在为 ${testName} 生成步骤...`);
        vscode.postMessage({
            command: 'generateSteps',
            testName: testName
        });
        addStatusEntry(`开始为 ${testName} 生成步骤`, 'info');
    };
    
    window.executeTest = function(testName) {
        showLoading(`正在执行测试 ${testName}...`);
        vscode.postMessage({
            command: 'executeTests',
            testNames: [testName]
        });
        addStatusEntry(`开始执行测试 ${testName}`, 'info');
    };
    
    // 恢复状态
    const state = vscode.getState();
    if (state?.activeTab) {
        switchTab(state.activeTab);
    }
})();