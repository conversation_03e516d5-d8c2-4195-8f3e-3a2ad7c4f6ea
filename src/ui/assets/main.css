/* 现代化设计系统 */
:root {
    --border-radius: 8px;
    --border-radius-small: 4px;
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);
    --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.vscode-body {
    font-family: var(--vscode-font-family, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
    font-size: var(--vscode-font-size, 13px);
    color: var(--vscode-foreground);
    background: linear-gradient(135deg, 
        var(--vscode-editor-background) 0%, 
        var(--vscode-sideBar-background) 100%);
    margin: 0;
    padding: 0;
    line-height: 1.5;
    min-height: 100vh;
}

.container {
    padding: var(--spacing-lg);
    max-width: 1400px;
    margin: 0 auto;
    min-height: 100vh;
}

/* 现代化头部 */
.header {
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--vscode-editor-background);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--vscode-panel-border);
}

.title {
    margin: 0 0 var(--spacing-md) 0;
    font-size: 24px;
    font-weight: 700;
    color: var(--vscode-foreground);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: linear-gradient(135deg, var(--vscode-focusBorder), var(--vscode-button-background));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title::before {
    content: "🧪";
    font-size: 28px;
    -webkit-text-fill-color: initial;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* 现代化标签页 */
.tabs {
    display: flex;
    gap: var(--spacing-xs);
    background: var(--vscode-sideBar-background);
    border-radius: var(--border-radius);
    padding: var(--spacing-xs);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--vscode-panel-border);
}

.tab-button {
    background: none;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    color: var(--vscode-tab-inactiveForeground);
    border-radius: var(--border-radius-small);
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: var(--transition);
    min-width: 120px;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.tab-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.tab-button:hover::before {
    left: 100%;
}

.tab-button:hover {
    background: var(--vscode-tab-hoverBackground);
    color: var(--vscode-tab-hoverForeground);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.tab-button.active {
    background: linear-gradient(135deg, var(--vscode-button-background), var(--vscode-focusBorder));
    color: white;
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.tab-button.active .tab-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.tab-icon {
    font-size: 16px;
    transition: var(--transition);
}

.tab-text {
    font-size: 14px;
    font-weight: 600;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 现代化卡片区域 */
.section {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--vscode-editor-background);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--vscode-panel-border);
    position: relative;
    overflow: hidden;
    transition: var(--transition);
}

.section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--vscode-focusBorder), var(--vscode-button-background));
}

.section:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--vscode-panel-border);
    position: relative;
}

.section-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, var(--vscode-focusBorder), transparent);
}

.section-header h3 {
    margin: 0;
    color: var(--vscode-foreground);
    font-size: 18px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    position: relative;
}

.section-header h3::before {
    content: '';
    width: 4px;
    height: 20px;
    background: linear-gradient(135deg, var(--vscode-focusBorder), var(--vscode-button-background));
    border-radius: 2px;
    margin-right: var(--spacing-xs);
}

.config-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.status-unknown {
    background-color: var(--vscode-charts-gray);
}

.status-valid {
    background-color: var(--vscode-testing-iconPassed);
}

.status-invalid {
    background-color: var(--vscode-testing-iconFailed);
}

.status-text {
    color: var(--vscode-descriptionForeground);
}

/* 现代化按钮系统 */
.controls {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
    margin-bottom: var(--spacing-md);
}

.status-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
    cursor: pointer;
    border-radius: var(--border-radius);
    font-size: 13px;
    font-family: inherit;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: var(--transition);
    min-height: 36px;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn:hover:not(:disabled) {
    background: var(--vscode-button-secondaryHoverBackground);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn:active:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    filter: grayscale(1);
}

.btn-primary {
    background: linear-gradient(135deg, var(--vscode-button-background), var(--vscode-focusBorder));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--vscode-button-hoverBackground), var(--vscode-focusBorder));
    box-shadow: var(--shadow-lg);
}

.btn-success {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-success:hover:not(:disabled) {
    background: linear-gradient(135deg, #16a34a, #15803d);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
    border: 1px solid var(--vscode-panel-border);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-danger:hover:not(:disabled) {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    box-shadow: var(--shadow-lg);
}

.btn-small {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 11px;
    min-height: 28px;
}

.btn-icon {
    font-size: 14px;
    transition: var(--transition);
}

.btn:hover .btn-icon {
    transform: scale(1.1);
}

.action-buttons {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
    justify-content: flex-start;
    margin-top: var(--spacing-md);
}

.test-list {
    min-height: 200px;
    border: 1px solid var(--vscode-input-border);
    border-radius: 2px;
    padding: 10px;
    background-color: var(--vscode-input-background);
}

.test-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid var(--vscode-panel-border);
    cursor: pointer;
}

.test-item:hover {
    background-color: var(--vscode-list-hoverBackground);
}

.test-item:last-child {
    border-bottom: none;
}

.test-checkbox {
    margin-right: 10px;
}

.test-name {
    flex: 1;
    font-weight: 500;
}

.test-status {
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 2px;
    margin-left: 10px;
}

.status-complete {
    background-color: var(--vscode-testing-iconPassed);
    color: white;
}

.status-missing-steps {
    background-color: var(--vscode-testing-iconQueued);
    color: white;
}

.status-missing-result {
    background-color: var(--vscode-testing-iconUnset);
    color: white;
}

.status-area {
    min-height: 150px;
    border: 1px solid var(--vscode-input-border);
    border-radius: 2px;
    padding: 10px;
    background-color: var(--vscode-input-background);
    font-family: var(--vscode-editor-font-family);
    font-size: 12px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--vscode-foreground);
    font-size: 13px;
}

.form-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--vscode-input-border);
    border-radius: 4px;
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    font-family: inherit;
    font-size: 13px;
    box-sizing: border-box;
    transition: border-color 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--vscode-focusBorder);
    box-shadow: 0 0 0 1px var(--vscode-focusBorder);
}

.form-input:hover {
    border-color: var(--vscode-input-border);
}

.form-input[type="password"] {
    font-family: monospace;
    letter-spacing: 2px;
}

.input-group {
    display: flex;
    gap: 8px;
    align-items: stretch;
}

.input-group .form-input {
    flex: 1;
}

.input-help {
    margin-top: 4px;
    font-size: 11px;
    color: var(--vscode-descriptionForeground);
    font-style: italic;
}

/* 现代化占位符 */
.placeholder {
    color: var(--vscode-descriptionForeground);
    text-align: center;
    margin: var(--spacing-xl) var(--spacing-lg);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    background: var(--vscode-sideBar-background);
    border-radius: var(--border-radius);
    border: 2px dashed var(--vscode-panel-border);
    transition: var(--transition);
}

.placeholder:hover {
    border-color: var(--vscode-focusBorder);
    background: var(--vscode-editor-background);
}

.placeholder-icon {
    font-size: 64px;
    opacity: 0.6;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.placeholder p {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    opacity: 0.8;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background-color: var(--vscode-progressBar-background);
    border-radius: 2px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background-color: var(--vscode-progressBar-background);
    transition: width 0.3s ease;
}

.error-message {
    color: var(--vscode-errorForeground);
    background-color: var(--vscode-inputValidation-errorBackground);
    border: 1px solid var(--vscode-inputValidation-errorBorder);
    padding: 8px;
    border-radius: 2px;
    margin: 10px 0;
}

.success-message {
    color: var(--vscode-testing-iconPassed);
    background-color: var(--vscode-terminal-ansiGreen);
    padding: 8px;
    border-radius: 2px;
    margin: 10px 0;
    opacity: 0.1;
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .controls {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 5px;
    }
}
/* 
通知系统 */
.notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 400px;
}

.notification {
    background-color: var(--vscode-notifications-background);
    border: 1px solid var(--vscode-notifications-border);
    border-radius: 4px;
    padding: 12px 16px;
    margin-bottom: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: flex-start;
    gap: 10px;
    animation: slideIn 0.3s ease;
}

.notification-success {
    border-left: 4px solid var(--vscode-testing-iconPassed);
}

.notification-error {
    border-left: 4px solid var(--vscode-testing-iconFailed);
}

.notification-warning {
    border-left: 4px solid var(--vscode-testing-iconQueued);
}

.notification-info {
    border-left: 4px solid var(--vscode-focusBorder);
}

.notification-icon {
    font-size: 16px;
    margin-top: 2px;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--vscode-notifications-foreground);
}

.notification-message {
    font-size: 13px;
    color: var(--vscode-notifications-foreground);
    margin: 0;
}

.notification-close {
    background: none;
    border: none;
    color: var(--vscode-notifications-foreground);
    cursor: pointer;
    font-size: 16px;
    padding: 0;
    opacity: 0.7;
}

.notification-close:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    backdrop-filter: blur(2px);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--vscode-progressBar-background);
    border-top: 4px solid var(--vscode-focusBorder);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

.loading-text {
    color: var(--vscode-foreground);
    font-size: 14px;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 现代化测试列表 */
.test-list {
    min-height: 300px;
    max-height: 500px;
    background: var(--vscode-editor-background);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--vscode-panel-border);
    overflow-y: auto;
}

.test-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--vscode-panel-border);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    background: var(--vscode-editor-background);
}

.test-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(135deg, var(--vscode-focusBorder), var(--vscode-button-background));
    transition: var(--transition);
}

.test-item:hover::before {
    width: 4px;
}

.test-item:hover {
    background: var(--vscode-list-hoverBackground);
    transform: translateX(4px);
    box-shadow: var(--shadow-sm);
}

.test-item:last-child {
    border-bottom: none;
}

.test-item.selected {
    background: var(--vscode-list-activeSelectionBackground);
    color: var(--vscode-list-activeSelectionForeground);
    transform: translateX(8px);
    box-shadow: var(--shadow-md);
}

.test-item.selected::before {
    width: 4px;
}

.test-checkbox {
    margin-right: var(--spacing-md);
    cursor: pointer;
    width: 18px;
    height: 18px;
    accent-color: var(--vscode-focusBorder);
    transform: scale(1.2);
}

.test-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.test-name {
    font-weight: 600;
    font-size: 15px;
    color: var(--vscode-foreground);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.test-name::before {
    content: '📄';
    font-size: 16px;
}

.test-path {
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
    font-family: var(--vscode-editor-font-family);
    opacity: 0.8;
}

.test-status {
    font-size: 10px;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 12px;
    margin-left: var(--spacing-md);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: var(--shadow-sm);
    min-width: 60px;
    text-align: center;
}

.status-complete {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    color: white;
}

.status-missing-steps {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.status-missing-result {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
}

/* 现代化状态区域 */
.status-area {
    min-height: 200px;
    max-height: 400px;
    background: var(--vscode-editor-background);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    font-family: var(--vscode-editor-font-family);
    font-size: 13px;
    overflow-y: auto;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--vscode-panel-border);
}

.status-entry {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-small);
    background: var(--vscode-sideBar-background);
    transition: var(--transition);
    border-left: 4px solid transparent;
}

.status-entry:hover {
    transform: translateX(4px);
    box-shadow: var(--shadow-sm);
}

.status-timestamp {
    color: var(--vscode-descriptionForeground);
    font-size: 11px;
    min-width: 70px;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
    background: var(--vscode-editor-background);
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
}

.status-message {
    flex: 1;
    color: var(--vscode-foreground);
    font-weight: 500;
    line-height: 1.4;
}

.status-success {
    border-left-color: #22c55e;
    background: linear-gradient(90deg, rgba(34, 197, 94, 0.1), var(--vscode-sideBar-background));
}

.status-error {
    border-left-color: #ef4444;
    background: linear-gradient(90deg, rgba(239, 68, 68, 0.1), var(--vscode-sideBar-background));
}

.status-info {
    border-left-color: var(--vscode-focusBorder);
    background: linear-gradient(90deg, rgba(0, 122, 255, 0.1), var(--vscode-sideBar-background));
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 12px;
    }
    
    .section {
        padding: 16px;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .controls,
    .action-buttons {
        flex-direction: column;
        width: 100%;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    .tabs {
        flex-direction: column;
        gap: 2px;
    }
    
    .tab-button {
        width: 100%;
        justify-content: flex-start;
    }
    
    .notifications {
        left: 12px;
        right: 12px;
        max-width: none;
    }
}

/* 高对比度主题支持 */
@media (prefers-contrast: high) {
    .btn {
        border-width: 2px;
    }
    
    .form-input:focus {
        box-shadow: 0 0 0 2px var(--vscode-focusBorder);
    }
    
    .test-item:hover {
        border: 1px solid var(--vscode-focusBorder);
    }
}

/* 减少动画的用户偏好支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}/
* 配置页面增强样式 */
.config-grid {
    display: grid;
    gap: var(--spacing-md);
}

.label-icon {
    margin-right: var(--spacing-xs);
    font-size: 14px;
}

.form-group label {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
    color: var(--vscode-foreground);
    font-size: 14px;
}

.form-input[type="select"], select.form-input {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
}

/* 目录信息卡片 */
.directory-info {
    margin-top: var(--spacing-md);
}

.info-card {
    background: var(--vscode-sideBar-background);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    border: 1px solid var(--vscode-panel-border);
    box-shadow: var(--shadow-sm);
}

.info-card h4 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--vscode-foreground);
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-md);
    background: var(--vscode-editor-background);
    border-radius: var(--border-radius);
    border: 1px solid var(--vscode-panel-border);
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.stat-number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: var(--vscode-focusBorder);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

/* 配置状态指示器增强 */
.config-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 12px;
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--vscode-sideBar-background);
    border-radius: 12px;
    border: 1px solid var(--vscode-panel-border);
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    position: relative;
}

.status-indicator::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    animation: pulse-ring 2s infinite;
}

@keyframes pulse-ring {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

.status-unknown {
    background-color: #6b7280;
}

.status-unknown::after {
    background-color: #6b7280;
}

.status-valid {
    background-color: #22c55e;
}

.status-valid::after {
    background-color: #22c55e;
}

.status-invalid {
    background-color: #ef4444;
}

.status-invalid::after {
    background-color: #ef4444;
}

.status-text {
    color: var(--vscode-foreground);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 增强的通知系统 */
.notification {
    background: var(--vscode-editor-background);
    border: 1px solid var(--vscode-panel-border);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    animation: slideInRight 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-success {
    border-left: 4px solid #22c55e;
    background: linear-gradient(90deg, rgba(34, 197, 94, 0.1), var(--vscode-editor-background));
}

.notification-error {
    border-left: 4px solid #ef4444;
    background: linear-gradient(90deg, rgba(239, 68, 68, 0.1), var(--vscode-editor-background));
}

.notification-warning {
    border-left: 4px solid #f59e0b;
    background: linear-gradient(90deg, rgba(245, 158, 11, 0.1), var(--vscode-editor-background));
}

.notification-info {
    border-left: 4px solid var(--vscode-focusBorder);
    background: linear-gradient(90deg, rgba(0, 122, 255, 0.1), var(--vscode-editor-background));
}

.notification-icon {
    font-size: 18px;
    margin-top: 2px;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
    color: var(--vscode-foreground);
    font-size: 14px;
}

.notification-message {
    font-size: 13px;
    color: var(--vscode-foreground);
    margin: 0;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: var(--vscode-foreground);
    cursor: pointer;
    font-size: 18px;
    padding: 0;
    opacity: 0.7;
    transition: var(--transition);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.notification-close:hover {
    opacity: 1;
    background: var(--vscode-button-secondaryHoverBackground);
}

/* 加载遮罩增强 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    backdrop-filter: blur(8px);
}

.loading-spinner {
    width: 48px;
    height: 48px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-top: 4px solid var(--vscode-focusBorder);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-md);
}

.loading-text {
    color: white;
    font-size: 16px;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* 滚动条美化 */
.test-list::-webkit-scrollbar,
.status-area::-webkit-scrollbar {
    width: 8px;
}

.test-list::-webkit-scrollbar-track,
.status-area::-webkit-scrollbar-track {
    background: var(--vscode-scrollbarSlider-background);
    border-radius: 4px;
}

.test-list::-webkit-scrollbar-thumb,
.status-area::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-hoverBackground);
    border-radius: 4px;
}

.test-list::-webkit-scrollbar-thumb:hover,
.status-area::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-activeBackground);
}

/* 焦点状态增强 */
.form-input:focus,
.btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--vscode-focusBorder);
    transform: translateY(-1px);
}

/* 动画性能优化 */
* {
    will-change: auto;
}

.btn,
.test-item,
.section,
.tab-button {
    will-change: transform;
}

/* 深色主题特殊处理 */
@media (prefers-color-scheme: dark) {
    .section::before {
        opacity: 0.8;
    }
    
    .loading-overlay {
        background: rgba(0, 0, 0, 0.8);
    }
}/* 测试项动
作按钮 */
.test-actions {
    display: flex;
    gap: var(--spacing-xs);
    opacity: 0;
    transition: var(--transition);
}

.test-item:hover .test-actions {
    opacity: 1;
}

.btn-mini {
    width: 24px;
    height: 24px;
    border: none;
    background: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
}

.btn-mini:hover {
    background: var(--vscode-button-background);
    color: white;
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

/* 测试项入场动画 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.test-item {
    animation: slideInUp 0.3s ease-out forwards;
}

/* 配置页面的小改进 */
.placeholder small {
    font-size: 14px;
    color: var(--vscode-descriptionForeground);
    margin-top: var(--spacing-xs);
    opacity: 0.8;
}

/* 改进表单输入框的视觉效果 */
.form-input {
    position: relative;
    transition: var(--transition);
}

.form-input:hover {
    border-color: var(--vscode-focusBorder);
}

.form-input:focus {
    border-color: var(--vscode-focusBorder);
    box-shadow: 0 0 0 1px var(--vscode-focusBorder);
}

/* 改进按钮的视觉层次 */
.btn-primary {
    position: relative;
    overflow: hidden;
}

.btn-primary::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn-primary:active::after {
    width: 300px;
    height: 300px;
}

/* 状态指示器的改进动画 */
.status-valid::after {
    animation: pulse-ring 2s infinite;
}

.status-invalid::after {
    animation: pulse-ring 1s infinite;
}

/* 改进滚动条在不同主题下的显示 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-background);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-hoverBackground);
}

/* 改进焦点可见性 */
.btn:focus-visible,
.form-input:focus-visible,
.test-checkbox:focus-visible {
    outline: 2px solid var(--vscode-focusBorder);
    outline-offset: 2px;
}

/* 改进高对比度模式支持 */
@media (prefers-contrast: high) {
    .section {
        border-width: 2px;
    }
    
    .btn {
        border-width: 2px;
        font-weight: 700;
    }
    
    .test-item {
        border-bottom-width: 2px;
    }
}

/* 改进打印样式 */
@media print {
    .btn,
    .loading-overlay,
    .notifications {
        display: none !important;
    }
    
    .section {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .test-item {
        break-inside: avoid;
    }
}