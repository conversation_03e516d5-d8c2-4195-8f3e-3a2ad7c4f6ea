import * as vscode from 'vscode';
import * as path from 'path';

export class MainPanel {
    public static currentPanel: MainPanel | undefined;
    private readonly _panel: vscode.WebviewPanel;
    private readonly _extensionUri: vscode.Uri;
    private _disposables: vscode.Disposable[] = [];

    public static createOrShow(extensionUri: vscode.Uri) {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;

        if (MainPanel.currentPanel) {
            MainPanel.currentPanel._panel.reveal(column);
            return;
        }

        const panel = vscode.window.createWebviewPanel(
            'aiTestPanel',
            'AI自动化测试',
            column || vscode.ViewColumn.One,
            {
                enableScripts: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(extensionUri, 'dist'),
                    vscode.Uri.joinPath(extensionUri, 'src')
                ]
            }
        );

        MainPanel.currentPanel = new MainPanel(panel, extensionUri);
    }

    private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri) {
        this._panel = panel;
        this._extensionUri = extensionUri;

        this._update();

        this._panel.onDidDispose(() => this.dispose(), null, this._disposables);

        this._panel.webview.onDidReceiveMessage(
            message => {
                switch (message.command) {
                    case 'scanDocuments':
                        this.handleScanDocuments();
                        break;
                    case 'generateSteps':
                        this.handleGenerateSteps(message.testName);
                        break;
                    case 'executeTests':
                        this.handleExecuteTests(message.testNames);
                        break;
                    case 'saveConfig':
                        this.handleSaveConfig(message.config);
                        break;
                    case 'testConnection':
                        this.handleTestConnection(message.config);
                        break;
                    case 'browseDirectory':
                        this.handleBrowseDirectory();
                        break;
                    case 'loadConfig':
                        this.handleLoadConfig();
                        break;
                }
            },
            null,
            this._disposables
        );
    }

    private async handleScanDocuments() {
        try {
            const result = await vscode.commands.executeCommand('ai-test.scanDocuments');
            this._panel.webview.postMessage({
                command: 'scanComplete',
                success: true,
                testGroups: result || []
            });
        } catch (error) {
            this._panel.webview.postMessage({
                command: 'scanComplete',
                success: false,
                error: error instanceof Error ? error.message : '扫描失败'
            });
        }
    }

    private async handleGenerateSteps(testName: string) {
        try {
            await vscode.commands.executeCommand('ai-test.generateSteps', testName);
            this._panel.webview.postMessage({
                command: 'generateComplete',
                success: true,
                testName
            });
        } catch (error) {
            this._panel.webview.postMessage({
                command: 'generateComplete',
                success: false,
                testName,
                error: error instanceof Error ? error.message : '生成失败'
            });
        }
    }

    private async handleExecuteTests(testNames: string[]) {
        try {
            await vscode.commands.executeCommand('ai-test.executeTests', testNames);
            this._panel.webview.postMessage({
                command: 'executeComplete',
                success: true,
                testNames
            });
        } catch (error) {
            this._panel.webview.postMessage({
                command: 'executeComplete',
                success: false,
                testNames,
                error: error instanceof Error ? error.message : '执行失败'
            });
        }
    }

    private async handleSaveConfig(config: any) {
        try {
            const configManager = (await import('../../managers/config.js')).ConfigurationManager.getInstance();
            await configManager.setAIConfig(config.ai);
            await configManager.setTestDirectory(config.testDirectory);
            
            this._panel.webview.postMessage({
                command: 'configSaved',
                success: true
            });
        } catch (error) {
            this._panel.webview.postMessage({
                command: 'configSaved',
                success: false,
                error: error instanceof Error ? error.message : '保存失败'
            });
        }
    }

    private async handleTestConnection(config: any) {
        try {
            // TODO: 实现AI服务连接测试
            // 这里暂时模拟成功，实际实现在后续任务中
            this._panel.webview.postMessage({
                command: 'testConnection',
                success: true
            });
        } catch (error) {
            this._panel.webview.postMessage({
                command: 'testConnection',
                success: false,
                error: error instanceof Error ? error.message : '连接测试失败'
            });
        }
    }

    private async handleBrowseDirectory() {
        try {
            const result = await vscode.window.showOpenDialog({
                canSelectFiles: false,
                canSelectFolders: true,
                canSelectMany: false,
                openLabel: '选择测试目录'
            });

            if (result && result[0]) {
                const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
                let relativePath = result[0].fsPath;
                
                if (workspaceFolder) {
                    relativePath = vscode.workspace.asRelativePath(result[0]);
                }

                this._panel.webview.postMessage({
                    command: 'browseDirectory',
                    success: true,
                    directory: relativePath
                });
            }
        } catch (error) {
            this._panel.webview.postMessage({
                command: 'browseDirectory',
                success: false,
                error: error instanceof Error ? error.message : '目录选择失败'
            });
        }
    }

    private async handleLoadConfig() {
        try {
            const configManager = (await import('../../managers/config.js')).ConfigurationManager.getInstance();
            const aiConfig = configManager.getAIConfig();
            const testDirectory = configManager.getTestDirectory();

            this._panel.webview.postMessage({
                command: 'loadConfig',
                success: true,
                config: {
                    ai: aiConfig,
                    testDirectory: testDirectory
                }
            });
        } catch (error) {
            this._panel.webview.postMessage({
                command: 'loadConfig',
                success: false,
                error: error instanceof Error ? error.message : '配置加载失败'
            });
        }
    }

    public dispose() {
        MainPanel.currentPanel = undefined;

        this._panel.dispose();

        while (this._disposables.length) {
            const x = this._disposables.pop();
            if (x) {
                x.dispose();
            }
        }
    }

    private _update() {
        const webview = this._panel.webview;
        this._panel.webview.html = this._getHtmlForWebview(webview);
    }

    private _getHtmlForWebview(webview: vscode.Webview) {
        // 尝试从dist目录加载，如果不存在则从src目录加载
        const scriptPath = vscode.Uri.joinPath(this._extensionUri, 'dist', 'ui', 'assets', 'main.js');
        const stylePath = vscode.Uri.joinPath(this._extensionUri, 'dist', 'ui', 'assets', 'main.css');
        
        const scriptUri = webview.asWebviewUri(scriptPath);
        const styleUri = webview.asWebviewUri(stylePath);

        const nonce = this.getNonce();

        return `<!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}'; img-src ${webview.cspSource} data:;">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <link href="${styleUri}" rel="stylesheet">
                <title>AI自动化测试</title>
                <!-- 调试信息 -->
                <style>
                    .debug-info {
                        position: fixed;
                        top: 10px;
                        right: 10px;
                        background: rgba(0,0,0,0.8);
                        color: white;
                        padding: 10px;
                        font-size: 10px;
                        border-radius: 4px;
                        z-index: 9999;
                        max-width: 300px;
                        word-break: break-all;
                    }
                </style>
            </head>
            <body class="vscode-body">
                <!-- 调试信息 -->
                <div class="debug-info">
                    <div>CSS: ${styleUri}</div>
                    <div>JS: ${scriptUri}</div>
                    <div>CSP: ${webview.cspSource}</div>
                </div>
                <div class="container">
                    <div class="header">
                        <h1 class="title">AI自动化测试</h1>
                        <div class="tabs">
                            <button class="tab-button active" data-tab="tests">
                                <span class="tab-icon">🧪</span>
                                <span class="tab-text">测试</span>
                            </button>
                            <button class="tab-button" data-tab="config">
                                <span class="tab-icon">⚙️</span>
                                <span class="tab-text">配置</span>
                            </button>
                        </div>
                    </div>
                    
                    <div id="tests" class="tab-content active">
                        <div class="section">
                            <div class="section-header">
                                <h3>测试文档管理</h3>
                                <div class="controls">
                                    <button id="scanBtn" class="btn btn-primary">
                                        <span class="btn-icon">🔍</span>
                                        扫描文档
                                    </button>
                                    <button id="selectAllBtn" class="btn btn-secondary">
                                        <span class="btn-icon">☑️</span>
                                        全选
                                    </button>
                                    <button id="executeBtn" class="btn btn-success" disabled>
                                        <span class="btn-icon">🚀</span>
                                        执行测试
                                    </button>
                                    <button id="generateBtn" class="btn btn-secondary">
                                        <span class="btn-icon">✨</span>
                                        生成步骤
                                    </button>
                                </div>
                            </div>
                            <div id="testList" class="test-list">
                                <div class="placeholder">
                                    <div class="placeholder-icon">🔍</div>
                                    <p>点击"扫描文档"开始发现测试文件</p>
                                    <small>支持 .md 测试文档和 .yaml 步骤文件</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="section">
                            <div class="section-header">
                                <h3>执行状态</h3>
                                <div class="status-controls">
                                    <button id="clearStatusBtn" class="btn btn-small">
                                        <span class="btn-icon">🗑️</span>
                                        清空
                                    </button>
                                    <button id="exportStatusBtn" class="btn btn-small">
                                        <span class="btn-icon">📤</span>
                                        导出
                                    </button>
                                </div>
                            </div>
                            <div id="statusArea" class="status-area">
                                <div class="placeholder">
                                    <div class="placeholder-icon">📊</div>
                                    <p>暂无执行状态</p>
                                    <small>测试执行日志将在这里显示</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="config" class="tab-content">
                        <div class="section">
                            <div class="section-header">
                                <h3>🤖 AI服务配置</h3>
                                <div class="config-status" id="aiConfigStatus">
                                    <span class="status-indicator status-unknown"></span>
                                    <span class="status-text">未配置</span>
                                </div>
                            </div>
                            <div class="config-grid">
                                <div class="form-group">
                                    <label for="apiUrl">
                                        <span class="label-icon">🌐</span>
                                        API地址
                                    </label>
                                    <input type="text" id="apiUrl" placeholder="https://api.openai.com/v1" class="form-input">
                                    <div class="input-help">输入OpenAI兼容的API地址</div>
                                </div>
                                <div class="form-group">
                                    <label for="apiKey">
                                        <span class="label-icon">🔑</span>
                                        API密钥
                                    </label>
                                    <input type="password" id="apiKey" placeholder="sk-..." class="form-input">
                                    <div class="input-help">您的API密钥将被安全存储</div>
                                </div>
                                <div class="form-group">
                                    <label for="model">
                                        <span class="label-icon">🧠</span>
                                        AI模型
                                    </label>
                                    <select id="model" class="form-input">
                                        <option value="gpt-3.5-turbo">GPT-3.5 Turbo (推荐)</option>
                                        <option value="gpt-4">GPT-4</option>
                                        <option value="gpt-4-turbo">GPT-4 Turbo</option>
                                        <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                                        <option value="claude-3-opus">Claude 3 Opus</option>
                                    </select>
                                    <div class="input-help">选择适合的AI模型进行测试步骤生成</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="section">
                            <div class="section-header">
                                <h3>📁 测试目录配置</h3>
                                <div class="config-status" id="dirConfigStatus">
                                    <span class="status-indicator status-unknown"></span>
                                    <span class="status-text">未配置</span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="testDirectory">
                                    <span class="label-icon">📂</span>
                                    测试目录路径
                                </label>
                                <div class="input-group">
                                    <input type="text" id="testDirectory" placeholder="./test-docs" class="form-input">
                                    <button id="browseBtn" class="btn btn-secondary">
                                        <span class="btn-icon">📁</span>
                                        浏览
                                    </button>
                                </div>
                                <div class="input-help">选择包含测试文档(.md)和步骤文件(.yaml)的目录</div>
                            </div>
                            
                            <div class="directory-info" id="directoryInfo" style="display: none;">
                                <div class="info-card">
                                    <h4>📊 目录统计</h4>
                                    <div class="stats-grid">
                                        <div class="stat-item">
                                            <span class="stat-number" id="testDocsCount">0</span>
                                            <span class="stat-label">测试文档</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-number" id="stepFilesCount">0</span>
                                            <span class="stat-label">步骤文件</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-number" id="resultFilesCount">0</span>
                                            <span class="stat-label">结果文件</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="section">
                            <div class="section-header">
                                <h3>⚡ 操作中心</h3>
                            </div>
                            <div class="action-buttons">
                                <button id="saveConfigBtn" class="btn btn-primary">
                                    <span class="btn-icon">💾</span>
                                    保存配置
                                </button>
                                <button id="testConnectionBtn" class="btn btn-secondary">
                                    <span class="btn-icon">🔗</span>
                                    测试连接
                                </button>
                                <button id="validateConfigBtn" class="btn btn-secondary">
                                    <span class="btn-icon">✅</span>
                                    验证配置
                                </button>
                                <button id="resetConfigBtn" class="btn btn-danger">
                                    <span class="btn-icon">🔄</span>
                                    重置配置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 通知容器 -->
                <div id="notifications" class="notifications"></div>
                
                <!-- 加载遮罩 -->
                <div id="loadingOverlay" class="loading-overlay" style="display: none;">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">处理中...</div>
                </div>
                
                <script nonce="${nonce}" src="${scriptUri}"></script>
            </body>
            </html>`;
    }

    private getNonce() {
        let text = '';
        const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        for (let i = 0; i < 32; i++) {
            text += possible.charAt(Math.floor(Math.random() * possible.length));
        }
        return text;
    }
}