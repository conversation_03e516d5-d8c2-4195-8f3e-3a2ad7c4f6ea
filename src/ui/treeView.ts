import * as vscode from 'vscode';
import { DocumentScanner, TestDocumentGroup, DocumentStatus } from '../managers/scanner';

export class TestTreeItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly group?: TestDocumentGroup,
        public readonly itemType?: 'directory' | 'test'
    ) {
        super(label, collapsibleState);

        if (group && itemType === 'test') {
            // 测试项目 - 不可展开，显示在一行
            this.tooltip = this.buildTooltip(group);
            this.description = this.getStatusDescription(group.status);
            this.contextValue = 'testItem';
            this.collapsibleState = vscode.TreeItemCollapsibleState.None;

            // 根据状态设置图标
            this.iconPath = this.getTestIcon(group);
        } else if (itemType === 'directory') {
            // 目录项目
            this.iconPath = new vscode.ThemeIcon('folder');
            this.contextValue = 'directory';
        } else {
            // 默认项目
            this.iconPath = new vscode.ThemeIcon('file');
        }
    }

    private buildTooltip(group: TestDocumentGroup): string {
        const parts = [`测试: ${group.name}`];

        if (group.testDoc) {
            parts.push('✓ 测试文档');
        }
        if (group.stepDoc) {
            parts.push('✓ 步骤文档');
        }
        if (group.resultDoc) {
            parts.push('✓ 结果文档');
        }

        return parts.join('\n');
    }

    private getTestIcon(group: TestDocumentGroup): vscode.ThemeIcon {
        // 如果没有 yaml 文件，显示感叹号
        if (!group.stepDoc) {
            return new vscode.ThemeIcon('warning', new vscode.ThemeColor('list.warningForeground'));
        }

        // 如果有 yaml 文件，显示可执行图标
        if (group.stepDoc && !group.resultDoc) {
            return new vscode.ThemeIcon('play', new vscode.ThemeColor('testing.iconQueued'));
        }

        // 如果有结果文档，显示完成图标
        if (group.resultDoc) {
            return new vscode.ThemeIcon('check', new vscode.ThemeColor('testing.iconPassed'));
        }

        return new vscode.ThemeIcon('circle-outline');
    }

    private getStatusDescription(status: DocumentStatus): string {
        switch (status) {
            case DocumentStatus.COMPLETE:
                return '已完成';
            case DocumentStatus.MISSING_STEPS:
                return '缺少步骤';
            case DocumentStatus.MISSING_RESULT:
                return '可执行';
            default:
                return '';
        }
    }
}

interface DirectoryNode {
    name: string;
    path: string;
    children: Map<string, DirectoryNode>;
    tests: TestDocumentGroup[];
}

export class TestTreeDataProvider implements vscode.TreeDataProvider<TestTreeItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<TestTreeItem | undefined | null | void> = new vscode.EventEmitter<TestTreeItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<TestTreeItem | undefined | null | void> = this._onDidChangeTreeData.event;

    private testGroups: TestDocumentGroup[] = [];
    private directoryTree: DirectoryNode | null = null;

    constructor(private scanner: DocumentScanner) {
        // 监听文档变化
        this.scanner.onDocumentsChanged((groups) => {
            console.log(`树视图收到文档变化通知: ${groups.length} 个测试组`);
            this.testGroups = groups;
            this.buildDirectoryTree();
            this.refresh();
        });

        // 初始化时获取当前的文档组
        this.testGroups = this.scanner.getDocumentGroups();
        console.log(`初始化时获取到 ${this.testGroups.length} 个测试组`);
        this.buildDirectoryTree();
    }

    private buildDirectoryTree(): void {
        console.log(`构建目录树，测试组数量: ${this.testGroups.length}`);

        this.directoryTree = {
            name: 'root',
            path: '',
            children: new Map(),
            tests: []
        };

        for (const group of this.testGroups) {
            console.log(`处理测试组: ${group.name}`);

            // 获取测试文档的目录路径
            const testDocPath = group.testDoc || group.stepDoc || group.resultDoc;
            if (!testDocPath) {
                console.log(`跳过测试组 ${group.name}：没有文档路径`);
                continue;
            }

            console.log(`测试组 ${group.name} 的文档路径: ${testDocPath}`);

            const relativePath = this.getRelativePath(testDocPath);
            console.log(`相对路径: ${relativePath}`);

            const pathParts = relativePath.split('/').filter(part => part.length > 0);
            console.log(`路径部分: ${pathParts.join(', ')}`);

            // 如果没有子目录，直接放在根目录
            if (pathParts.length <= 1) {
                console.log(`将 ${group.name} 添加到根目录`);
                this.directoryTree.tests.push(group);
            } else {
                // 有子目录，构建目录树
                let currentNode = this.directoryTree;
                const dirParts = pathParts.slice(0, -1); // 排除文件名部分
                console.log(`目录部分: ${dirParts.join(', ')}`);

                for (const dirName of dirParts) {
                    if (!currentNode.children.has(dirName)) {
                        console.log(`创建目录节点: ${dirName}`);
                        currentNode.children.set(dirName, {
                            name: dirName,
                            path: currentNode.path ? `${currentNode.path}/${dirName}` : dirName,
                            children: new Map(),
                            tests: []
                        });
                    }
                    currentNode = currentNode.children.get(dirName)!;
                }

                console.log(`将 ${group.name} 添加到目录 ${currentNode.name}`);
                currentNode.tests.push(group);
            }
        }

        console.log(`目录树构建完成，根目录测试数: ${this.directoryTree.tests.length}，子目录数: ${this.directoryTree.children.size}`);
    }

    private getRelativePath(absolutePath: string): string {
        // 简化处理，获取相对于测试目录的路径
        const path = require('path');
        const parts = absolutePath.split(path.sep);
        const testDocsIndex = parts.findIndex(part => part === 'test-docs');
        if (testDocsIndex >= 0) {
            return parts.slice(testDocsIndex + 1).join('/');
        }
        return path.basename(absolutePath);
    }

    refresh(): void {
        this._onDidChangeTreeData.fire();
    }

    getTreeItem(element: TestTreeItem): vscode.TreeItem {
        return element;
    }

    getChildren(element?: TestTreeItem): Thenable<TestTreeItem[]> {
        if (!element) {
            // 根节点
            return this.getRootChildren();
        } else if (element.itemType === 'directory') {
            // 目录节点
            return this.getDirectoryChildren(element.label);
        }

        return Promise.resolve([]);
    }

    private getRootChildren(): Promise<TestTreeItem[]> {
        console.log('获取根节点子项');

        if (!this.directoryTree) {
            console.log('目录树为空');
            return Promise.resolve([
                new TestTreeItem('暂无测试文档', vscode.TreeItemCollapsibleState.None)
            ]);
        }

        const children: TestTreeItem[] = [];

        // 添加子目录
        console.log(`添加 ${this.directoryTree.children.size} 个子目录`);
        for (const [dirName, dirNode] of this.directoryTree.children) {
            console.log(`添加目录: ${dirName}`);
            children.push(new TestTreeItem(
                dirName,
                vscode.TreeItemCollapsibleState.Collapsed,
                undefined,
                'directory'
            ));
        }

        // 添加根目录下的测试
        console.log(`添加 ${this.directoryTree.tests.length} 个根目录测试`);
        for (const group of this.directoryTree.tests) {
            console.log(`添加测试: ${group.name}`);
            children.push(new TestTreeItem(
                group.name,
                vscode.TreeItemCollapsibleState.None,
                group,
                'test'
            ));
        }

        console.log(`总共生成 ${children.length} 个子项`);

        if (children.length === 0) {
            console.log('没有子项，显示暂无测试文档');
            return Promise.resolve([
                new TestTreeItem('暂无测试文档', vscode.TreeItemCollapsibleState.None)
            ]);
        }

        return Promise.resolve(children);
    }

    private getDirectoryChildren(dirName: string): Promise<TestTreeItem[]> {
        if (!this.directoryTree) {
            return Promise.resolve([]);
        }

        const dirNode = this.findDirectoryNode(this.directoryTree, dirName);
        if (!dirNode) {
            return Promise.resolve([]);
        }

        const children: TestTreeItem[] = [];

        // 添加子目录
        for (const [childDirName, childDirNode] of dirNode.children) {
            children.push(new TestTreeItem(
                childDirName,
                vscode.TreeItemCollapsibleState.Collapsed,
                undefined,
                'directory'
            ));
        }

        // 添加测试
        for (const group of dirNode.tests) {
            children.push(new TestTreeItem(
                group.name,
                vscode.TreeItemCollapsibleState.None,
                group,
                'test'
            ));
        }

        return Promise.resolve(children);
    }

    private findDirectoryNode(root: DirectoryNode, targetName: string): DirectoryNode | null {
        if (root.name === targetName) {
            return root;
        }

        for (const [, child] of root.children) {
            const found = this.findDirectoryNode(child, targetName);
            if (found) {
                return found;
            }
        }

        return null;
    }

    public updateTestGroups(groups: TestDocumentGroup[]): void {
        console.log(`手动更新测试组: ${groups.length} 个`);
        this.testGroups = groups;
        this.buildDirectoryTree();
    }

    public async scanTests(): Promise<void> {
        try {
            const { ConfigurationManager } = await import('../managers/config.js');
            const configManager = ConfigurationManager.getInstance();
            const testDirectory = configManager.getTestDirectory();

            if (!testDirectory) {
                vscode.window.showWarningMessage('请先配置测试目录');
                return;
            }

            const groups = await this.scanner.scanDirectory(testDirectory);
            this.updateTestGroups(groups);
            this.refresh();

            vscode.window.showInformationMessage(`扫描完成，发现 ${groups.length} 个测试文档组`);
        } catch (error) {
            const message = error instanceof Error ? error.message : '扫描失败';
            vscode.window.showErrorMessage(`文档扫描失败: ${message}`);
        }
    }
}