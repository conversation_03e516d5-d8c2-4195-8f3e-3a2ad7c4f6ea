const esbuild = require("esbuild");
const fs = require("fs");
const path = require("path");

const production = process.argv.includes('--production');
const watch = process.argv.includes('--watch');

/**
 * @type {import('esbuild').Plugin}
 */
const esbuildProblemMatcherPlugin = {
	name: 'esbuild-problem-matcher',

	setup(build) {
		build.onStart(() => {
			console.log('[watch] build started');
		});
		build.onEnd((result) => {
			result.errors.forEach(({ text, location }) => {
				console.error(`✘ [ERROR] ${text}`);
				console.error(`    ${location.file}:${location.line}:${location.column}:`);
			});
			console.log('[watch] build finished');
		});
	},
};

/**
 * @type {import('esbuild').Plugin}
 */
const copyAssetsPlugin = {
	name: 'copy-assets',
	setup(build) {
		build.onEnd(() => {
			// 确保目标目录存在
			const targetDir = path.join(__dirname, 'dist', 'ui', 'assets');
			if (!fs.existsSync(targetDir)) {
				fs.mkdirSync(targetDir, { recursive: true });
			}

			// 复制CSS文件
			const cssSource = path.join(__dirname, 'src', 'ui', 'assets', 'main.css');
			const cssTarget = path.join(targetDir, 'main.css');
			if (fs.existsSync(cssSource)) {
				fs.copyFileSync(cssSource, cssTarget);
				console.log('✓ Copied main.css to dist/ui/assets/');
			}

			// 复制JS文件
			const jsSource = path.join(__dirname, 'src', 'ui', 'assets', 'main.js');
			const jsTarget = path.join(targetDir, 'main.js');
			if (fs.existsSync(jsSource)) {
				fs.copyFileSync(jsSource, jsTarget);
				console.log('✓ Copied main.js to dist/ui/assets/');
			}
		});
	},
};

async function main() {
	const ctx = await esbuild.context({
		entryPoints: [
			'src/extension.ts'
		],
		bundle: true,
		format: 'cjs',
		minify: production,
		sourcemap: !production,
		sourcesContent: false,
		platform: 'node',
		outfile: 'dist/extension.js',
		external: ['vscode'],
		logLevel: 'silent',
		plugins: [
			/* add to the end of plugins array */
			esbuildProblemMatcherPlugin,
			copyAssetsPlugin,
		],
	});
	if (watch) {
		await ctx.watch();
	} else {
		await ctx.rebuild();
		await ctx.dispose();
	}
}

main().catch(e => {
	console.error(e);
	process.exit(1);
});
