# 架构设计文档

## 概述

本文档描述了VS Code AI自动化测试插件的架构设计。该插件通过AI驱动Playwright的MCP进行自然语义自动化测试，为开发者提供智能化的测试解决方案。

插件的核心功能包括：
- 测试文档扫描与管理
- AI驱动的测试步骤生成
- 基于Playwright MCP的测试执行
- 实时测试状态反馈

## 架构

### 整体架构

```mermaid
graph TB
    subgraph "VS Code Extension"
        UI[用户界面层]
        Core[核心业务层]
        Storage[存储管理层]
    end
    
    subgraph "外部服务"
        AI[AI服务<br/>OpenAI兼容API]
        MCP[Playwright MCP服务]
        FS[文件系统]
    end
    
    UI --> Core
    Core --> Storage
    Core --> AI
    Core --> MCP
    Storage --> FS
```

### 分层架构

#### 1. 用户界面层 (UI Layer)
- **WebView Panel**: 主要的插件界面容器
- **测试标签页**: 测试文档管理和执行界面
- **配置标签页**: AI和测试目录配置界面
- **状态栏**: 显示测试执行状态

#### 2. 核心业务层 (Core Business Layer)
- **文档管理器**: 负责扫描、解析和管理测试文档
- **AI服务客户端**: 与AI服务通信，生成测试步骤
- **测试执行器**: 通过MCP协议执行Playwright测试
- **配置管理器**: 管理插件配置信息

#### 3. 存储管理层 (Storage Layer)
- **文件系统接口**: 统一的文件操作接口
- **配置存储**: VS Code配置存储
- **缓存管理**: 测试结果和状态缓存

## 组件和接口

### 核心组件

#### 1. DocumentManager (文档管理器)
```typescript
interface DocumentManager {
  scanDirectory(path: string): Promise<TestDocumentGroup[]>
  getDocumentGroups(): TestDocumentGroup[]
  watchDirectory(path: string): void
  refreshDocuments(): Promise<void>
}

interface TestDocumentGroup {
  name: string
  testDoc?: string      // *.md
  stepDoc?: string      // *.yaml
  resultDoc?: string    // *.result.md
  status: DocumentStatus
}

enum DocumentStatus {
  COMPLETE = 'complete',           // 三个文档都存在
  MISSING_STEPS = 'missing_steps', // 缺少步骤文档
  MISSING_RESULT = 'missing_result' // 缺少结果文档
}
```

#### 2. AIServiceClient (AI服务客户端)
```typescript
interface AIServiceClient {
  generateSteps(testContent: string): Promise<string>
  configure(config: AIConfig): void
  testConnection(): Promise<boolean>
}

interface AIConfig {
  apiUrl: string
  apiKey: string
  model: string
}
```

#### 3. TestExecutor (测试执行器)
```typescript
interface TestExecutor {
  executeTest(stepFilePath: string): Promise<TestResult>
  executeMultipleTests(stepFilePaths: string[]): Promise<TestResult[]>
  cancelExecution(): void
  onProgress(callback: (progress: TestProgress) => void): void
}

interface TestResult {
  testName: string
  status: 'success' | 'failed' | 'cancelled'
  steps: StepResult[]
  duration: number
  error?: string
  screenshots?: string[]
}

interface TestProgress {
  testName: string
  currentStep: number
  totalSteps: number
  stepDescription: string
}
```

#### 4. ConfigurationManager (配置管理器)
```typescript
interface ConfigurationManager {
  getAIConfig(): AIConfig
  setAIConfig(config: AIConfig): Promise<void>
  getTestDirectory(): string
  setTestDirectory(path: string): Promise<void>
  validateConfig(): Promise<ValidationResult>
}
```

### 用户界面组件

#### 1. TestPanel (测试面板)
- 测试文档列表显示
- 测试选择和执行控制
- 实时进度显示
- 结果查看

#### 2. ConfigPanel (配置面板)
- AI服务配置表单
- 测试目录选择器
- 配置验证和保存

#### 3. StatusProvider (状态提供器)
- 测试执行状态显示
- 进度条和通知

## 数据模型

### 测试步骤文档格式 (YAML)
```yaml
name: "用户登录测试"
description: "验证用户能够成功登录系统"
target: "https://example.com"
steps:
  - goal: "打开登录页面"
    operation: "导航"
    content: "访问系统首页"
  
  - goal: "进入登录界面"
    operation: "点击"
    content: "登录按钮"
  
  - goal: "输入用户凭据"
    operation: "填写"
    content: "用户名: testuser, 密码: password123"
  
  - goal: "提交登录信息"
    operation: "点击"
    content: "提交按钮"
  
  - goal: "验证登录成功"
    operation: "检查"
    content: "页面显示欢迎信息或用户名"
```

### 测试结果文档格式 (Markdown)
```markdown
# 测试结果报告

**测试名称**: 用户登录测试
**执行时间**: 2024-01-15 10:30:00
**执行时长**: 15.2秒
**状态**: 成功

## 步骤执行详情

### 步骤 1: 导航到目标页面
- **状态**: 成功
- **耗时**: 2.1秒
- **描述**: 成功导航到 https://example.com

### 步骤 2: 点击登录按钮
- **状态**: 成功
- **耗时**: 0.5秒
- **截图**: ![screenshot](./screenshots/step2.png)

## 总结
测试执行成功，所有步骤均按预期完成。
```

## 错误处理

### 错误分类
1. **配置错误**: AI服务配置无效、测试目录不存在
2. **网络错误**: AI服务连接失败、MCP服务不可用
3. **文件系统错误**: 文件读写权限问题、磁盘空间不足
4. **测试执行错误**: 页面元素未找到、断言失败
5. **解析错误**: YAML格式错误、Markdown解析失败

### 错误处理策略
```typescript
interface ErrorHandler {
  handleConfigurationError(error: ConfigurationError): void
  handleNetworkError(error: NetworkError): void
  handleFileSystemError(error: FileSystemError): void
  handleTestExecutionError(error: TestExecutionError): void
  handleParsingError(error: ParsingError): void
}

class ErrorRecovery {
  retryWithBackoff(operation: () => Promise<any>, maxRetries: number): Promise<any>
  fallbackToCache(key: string): any
  notifyUser(message: string, severity: 'info' | 'warning' | 'error'): void
}
```

## 测试策略

### 单元测试
- **DocumentManager**: 文档扫描和解析逻辑
- **AIServiceClient**: API调用和响应处理
- **TestExecutor**: 测试步骤执行逻辑
- **ConfigurationManager**: 配置验证和存储

### 集成测试
- **AI服务集成**: 模拟AI服务响应，测试步骤生成
- **MCP集成**: 模拟Playwright MCP服务，测试执行流程
- **文件系统集成**: 测试文档读写和监听功能

### 端到端测试
- **完整工作流**: 从文档扫描到测试执行的完整流程
- **用户界面**: WebView面板的交互和状态更新
- **错误场景**: 各种异常情况的处理

### 测试工具和框架
- **Jest**: 单元测试和集成测试框架
- **VS Code Test Runner**: 插件测试环境
- **Mock服务**: 模拟外部依赖服务
- **测试数据**: 标准化的测试文档和配置

## 性能考虑

### 优化策略
1. **文档扫描优化**: 使用文件系统监听，避免频繁全量扫描
2. **AI调用优化**: 实现请求缓存和批量处理
3. **测试执行优化**: 支持并行执行多个测试
4. **内存管理**: 及时清理测试结果和临时文件

### 监控指标
- 文档扫描耗时
- AI服务响应时间
- 测试执行时长
- 内存使用情况
- 错误率统计

## 安全考虑

### 数据安全
- AI API密钥加密存储
- 测试结果敏感信息脱敏
- 文件访问权限控制

### 网络安全
- HTTPS通信加密
- API请求签名验证
- 超时和重试机制

### 代码安全
- 输入验证和清理
- SQL注入防护（如适用）
- XSS防护（WebView内容）