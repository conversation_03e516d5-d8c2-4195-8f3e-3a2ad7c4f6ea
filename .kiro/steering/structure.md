# Project Structure

## Root Directory Organization

```
/
├── src/                    # Source code
│   ├── extension.ts        # Main extension entry point
│   ├── managers/           # Core business logic managers
│   │   ├── config.ts       # Configuration management
│   │   ├── scanner.ts      # Document scanning and monitoring
│   │   ├── ai.ts          # AI service integration
│   │   └── executor.ts     # Test execution engine
│   ├── ui/                 # WebView UI components
│   │   ├── panels/         # WebView panel implementations
│   │   ├── assets/         # CSS, JS, and static assets
│   │   └── components/     # Reusable UI components
│   ├── models/             # Data models and interfaces
│   └── utils/              # Utility functions and helpers
├── package.json            # Extension manifest and dependencies
├── tsconfig.json          # TypeScript configuration
└── .vscodeignore          # Files to exclude from packaging
```

## File Naming Conventions

- **Test Documents**: `*.md` - Natural language test descriptions
- **Step Documents**: `*.yaml` - Structured test steps for execution
- **Result Documents**: `*.result.md` - Test execution results and reports
- **TypeScript Files**: PascalCase for classes, camelCase for functions/variables
- **UI Components**: kebab-case for HTML/CSS, camelCase for JavaScript

## Key Architectural Components

### Managers
- **ConfigManager**: Handles AI service and directory configuration
- **DocumentScanner**: Scans and monitors test document directories
- **AIService**: Integrates with OpenAI-compatible APIs for step generation
- **TestExecutor**: Manages test execution through Playwright MCP

### UI Structure
- **Main Panel**: Two-tab interface (Tests and Configuration)
- **Test Tab**: Document list, selection controls, execution status
- **Config Tab**: AI service settings and directory configuration

### Data Flow
1. Scanner discovers and groups related documents
2. AI service generates missing step documents
3. Executor runs tests via MCP protocol
4. Results are generated and displayed in real-time

## Development Guidelines

- Use TypeScript strict mode for type safety
- Implement proper error handling with user-friendly messages
- Follow VS Code extension best practices for UI and UX
- Maintain separation between UI logic and business logic
- Use async/await patterns for all asynchronous operations