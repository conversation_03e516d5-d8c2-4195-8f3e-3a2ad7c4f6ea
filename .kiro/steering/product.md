# Product Overview

This is a VS Code extension that provides AI-driven automated testing through Playwright MCP (Model Context Protocol) integration. The extension enables developers to:

- Scan and manage test documentation in specified directories
- Generate test steps automatically using AI from natural language test descriptions
- Execute automated tests through Playwright MCP integration
- Generate detailed test result reports with screenshots and error information

## Key Features

- **Document Management**: Automatically scans and organizes test documents (.md), step files (.yaml), and result files (.result.md)
- **AI Integration**: Uses OpenAI-compatible APIs to generate structured test steps from natural language descriptions
- **MCP Integration**: Executes tests through Playwright MCP protocol for browser automation
- **Real-time Feedback**: Provides live progress updates and status information during test execution
- **Parallel Execution**: Supports running multiple tests concurrently with queue management

## Target Users

Developers and QA engineers who want to streamline their testing workflow by combining natural language test descriptions with automated browser testing capabilities.