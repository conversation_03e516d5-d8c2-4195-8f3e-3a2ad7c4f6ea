# Technology Stack

## Core Technologies

- **Platform**: VS Code Extension
- **Language**: TypeScript
- **UI Framework**: WebView panels with HTML/CSS/JavaScript
- **Testing Framework**: Playwright (via MCP integration)
- **AI Integration**: OpenAI-compatible API clients
- **Protocol**: Model Context Protocol (MCP) for Playwright communication

## Key Dependencies

- VS Code Extension API
- TypeScript compiler and tooling
- HTTP clients for AI service integration
- YAML parsing libraries for step document processing
- File system watchers for document monitoring
- WebView communication APIs

## Build System

The project uses standard VS Code extension development practices:

- TypeScript compilation with `tsc`
- VS Code extension packaging with `vsce`
- Standard npm/yarn package management

## Common Commands

```bash
# Development
npm install          # Install dependencies
npm run compile      # Compile TypeScript
npm run watch        # Watch mode for development

# Packaging
npm run package      # Package extension for distribution
npm run publish      # Publish to VS Code marketplace
```

## Architecture Patterns

- **MVC Pattern**: Separation of WebView UI, business logic, and data models
- **Event-Driven**: Real-time status updates through event emitters
- **Plugin Architecture**: Modular design with separate managers for configuration, scanning, AI, and execution
- **Queue Management**: Async task queuing for parallel test execution