{"name": "ai-test", "displayName": "AI自动化测试", "description": "通过AI驱动Playwright的MCP进行自然语义自动化测试", "version": "0.0.1", "publisher": "ai-test", "repository": {"type": "git", "url": "https://github.com/ai-test/ai-auto-test.git"}, "engines": {"vscode": "^1.74.0"}, "categories": ["Testing"], "keywords": ["testing", "automation", "playwright", "ai", "mcp"], "activationEvents": [], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "ai-test.openPanel", "title": "打开AI测试面板", "category": "AI测试"}, {"command": "ai-test.scanDocuments", "title": "扫描测试文档", "category": "AI测试"}, {"command": "ai-test.generateSteps", "title": "生成测试步骤", "category": "AI测试"}, {"command": "ai-test.executeTests", "title": "执行测试", "category": "AI测试"}, {"command": "ai-test.refreshTests", "title": "刷新", "icon": "$(refresh)"}, {"command": "ai-test.debugConfig", "title": "调试配置信息", "category": "AI测试"}, {"command": "ai-test.generateStepsForTest", "title": "生成测试步骤", "icon": "$(add)"}, {"command": "ai-test.executeTest", "title": "执行测试", "icon": "$(play)"}], "viewsContainers": {"activitybar": [{"id": "ai-test", "title": "AI测试", "icon": "$(beaker)"}]}, "views": {"ai-test": [{"id": "ai-test.testExplorer", "name": "测试资源管理器"}]}, "configuration": {"title": "AI测试配置", "properties": {"ai-test.testDirectory": {"type": "string", "default": "", "description": "测试文档目录路径"}, "ai-test.aiService.apiUrl": {"type": "string", "default": "", "description": "AI服务API地址"}, "ai-test.aiService.apiKey": {"type": "string", "default": "", "description": "AI服务API密钥"}, "ai-test.aiService.model": {"type": "string", "default": "gpt-3.5-turbo", "description": "AI模型名称"}}}, "menus": {"view/title": [{"command": "ai-test.refreshTests", "when": "view == ai-test.testExplorer", "group": "navigation"}], "view/item/context": [{"command": "ai-test.generateStepsForTest", "when": "view == ai-test.testExplorer && viewItem == testItem", "group": "inline"}, {"command": "ai-test.executeTest", "when": "view == ai-test.testExplorer && viewItem == testItem", "group": "inline"}]}}, "scripts": {"vscode:prepublish": "pnpm run package", "compile": "pnpm run check-types && pnpm run lint && node esbuild.js", "watch": "npm-run-all -p watch:*", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "package": "pnpm run check-types && pnpm run lint && node esbuild.js --production", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "pnpm run compile-tests && pnpm run compile && pnpm run lint", "check-types": "tsc --noEmit", "lint": "eslint src", "test": "vscode-test", "vsce:package": "vsce package", "vsce:publish": "vsce publish"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.16.0", "axios": "^1.7.9", "chokidar": "^4.0.1", "js-yaml": "^4.1.0"}, "devDependencies": {"@types/js-yaml": "^4.0.9", "@types/mocha": "^10.0.10", "@types/node": "22.x", "@types/vscode": "^1.74.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2", "@vscode/vsce": "^3.6.0", "esbuild": "^0.25.3", "eslint": "^9.25.1", "npm-run-all": "^4.1.5", "typescript": "^5.8.3"}}