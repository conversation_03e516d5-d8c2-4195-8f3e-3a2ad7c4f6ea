// 简单的验证脚本，检查扫描器的基本逻辑
const fs = require('fs');
const path = require('path');

function verifyScanner() {
    console.log('验证文档扫描器逻辑...');
    
    const testDir = path.join(__dirname, 'test-docs');
    const files = fs.readdirSync(testDir);
    
    console.log('\n发现的文件:');
    files.forEach(file => {
        console.log(`  - ${file}`);
    });
    
    // 模拟扫描器的分组逻辑
    const groups = new Map();
    
    files.forEach(fileName => {
        const ext = path.extname(fileName);
        let baseName = null;
        let fileType = null;
        
        if (fileName.endsWith('.result.md')) {
            baseName = fileName.replace('.result.md', '');
            fileType = 'result';
        } else if (ext === '.md') {
            baseName = fileName.replace('.md', '');
            fileType = 'test';
        } else if (ext === '.yaml' || ext === '.yml') {
            baseName = fileName.replace(ext, '');
            fileType = 'step';
        }
        
        if (fileType && baseName) {
            if (!groups.has(baseName)) {
                groups.set(baseName, {
                    name: baseName,
                    testDoc: null,
                    stepDoc: null,
                    resultDoc: null
                });
            }
            
            const group = groups.get(baseName);
            const fullPath = path.join(testDir, fileName);
            
            switch (fileType) {
                case 'test':
                    group.testDoc = fullPath;
                    break;
                case 'step':
                    group.stepDoc = fullPath;
                    break;
                case 'result':
                    group.resultDoc = fullPath;
                    break;
            }
        }
    });
    
    console.log(`\n发现 ${groups.size} 个测试文档组:`);
    
    groups.forEach((group, name) => {
        console.log(`\n测试组: ${name}`);
        console.log(`  测试文档: ${group.testDoc ? '✓' : '✗'}`);
        console.log(`  步骤文档: ${group.stepDoc ? '✓' : '✗'}`);
        console.log(`  结果文档: ${group.resultDoc ? '✓' : '✗'}`);
        
        // 确定状态
        let status;
        if (group.testDoc && group.stepDoc && group.resultDoc) {
            status = 'complete';
        } else if (group.testDoc && group.stepDoc) {
            status = 'missing_result';
        } else {
            status = 'missing_steps';
        }
        console.log(`  状态: ${status}`);
    });
    
    console.log('\n✅ 扫描器逻辑验证完成！');
}

verifyScanner();